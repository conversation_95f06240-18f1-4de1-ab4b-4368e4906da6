"""
Google APIs integration (Search Console and Analytics)
"""
import pandas as pd
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from google.oauth2 import service_account
from googleapiclient import discovery
from typing import List, Dict, Any, Optional

from src.config.settings import settings
from src.utils.logging import get_logger

logger = get_logger(__name__)


class GoogleSearchConsoleClient:
    """Client for Google Search Console API"""
    
    def __init__(self, service_account_file: str):
        self.credentials = service_account.Credentials.from_service_account_file(service_account_file)
        self.service = discovery.build('searchconsole', 'v1', credentials=self.credentials)
    
    def get_data_by_month(self, domain_property: str, start_date: str, end_date: str, 
                         dimensions: List[str], metrics: List[str]) -> pd.DataFrame:
        """Fetch GSC data grouped by month"""
        logger.info(f"Fetching GSC data for {domain_property} from {start_date} to {end_date}")
        
        data = []
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        current = start
        
        while current <= end:
            next_month = current + relativedelta(months=1)
            if next_month > end:
                next_month = end + timedelta(days=1)
            
            month_start = current.strftime('%Y-%m-%d')
            month_end = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
            
            try:
                request = {
                    'startDate': month_start,
                    'endDate': month_end,
                    'dimensions': dimensions,
                    'rowLimit': 25000
                }
                
                response = self.service.searchanalytics().query(
                    siteUrl=domain_property, 
                    body=request
                ).execute()
                
                if 'rows' in response:
                    for row in response['rows']:
                        row_data = row['keys'] + [row['clicks'], row['impressions'], 
                                                row['ctr'], row['position']] + [current.strftime('%Y-%m')]
                        data.append(row_data)
                        
                logger.info(f"Fetched {len(response.get('rows', []))} rows for {month_start} to {month_end}")
                
            except Exception as e:
                logger.error(f"Error fetching GSC data for {month_start} to {month_end}: {e}")
            
            current = next_month
        
        columns = dimensions + ['clicks', 'impressions', 'ctr', 'position', 'Month']
        df = pd.DataFrame(data, columns=columns)

        # Rename columns to match database schema (uppercase)
        column_mapping = {
            'query': 'Keyword',
            'page': 'URL',
            'clicks': 'Clicks',
            'impressions': 'Impressions',
            'ctr': 'CTR',
            'position': 'Position'
        }
        df = df.rename(columns=column_mapping)

        return df


class GoogleAnalyticsClient:
    """Client for Google Analytics 4 API"""
    
    def __init__(self, service_account_file: str):
        self.credentials = service_account.Credentials.from_service_account_file(service_account_file)
        self.service = discovery.build('analyticsdata', 'v1beta', credentials=self.credentials)
    
    def get_data_by_month(self, property_id: str, start_date: str, end_date: str,
                         dimensions: List[str], metrics: List[str]) -> pd.DataFrame:
        """Fetch GA4 data grouped by month"""
        logger.info(f"Fetching GA4 data for property {property_id} from {start_date} to {end_date}")
        
        data = []
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        current = start
        
        while current <= end:
            next_month = current + relativedelta(months=1)
            if next_month > end:
                next_month = end + timedelta(days=1)
            
            month_start = current.strftime('%Y-%m-%d')
            month_end = (next_month - timedelta(days=1)).strftime('%Y-%m-%d')
            
            try:
                request = {
                    'dateRanges': [{'startDate': month_start, 'endDate': month_end}],
                    'dimensions': [{'name': dim} for dim in dimensions],
                    'metrics': [{'name': metric} for metric in metrics],
                    'limit': 100000
                }

                response = self.service.properties().runReport(property=f'properties/{property_id}', body=request).execute()
                
                if 'rows' in response:
                    for row in response['rows']:
                        dimension_values = [dim_value['value'] for dim_value in row['dimensionValues']]
                        metric_values = [metric_value['value'] for metric_value in row['metricValues']]
                        row_data = dimension_values + metric_values + [current.strftime('%Y-%m')]
                        data.append(row_data)
                
                logger.info(f"Fetched {len(response.get('rows', []))} rows for {month_start} to {month_end}")
                
            except Exception as e:
                logger.error(f"Error fetching GA4 data for {month_start} to {month_end}: {e}")
            
            current = next_month
        
        columns = dimensions + metrics + ['Month']
        df = pd.DataFrame(data, columns=columns)

        # Rename columns to match database schema
        column_mapping = {
            'pagePath': 'URL',
            'sessions': 'Sessions',
            'pageviews': 'Google Analytics Page Views',
            'bounceRate': 'Bounce Rate',
            'avgSessionDuration': 'Avg Session Duration'
        }
        df = df.rename(columns=column_mapping)

        return df
