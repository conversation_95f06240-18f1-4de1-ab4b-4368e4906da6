<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SEO Site Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
  <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
    .card { border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
    .d-none { display: none; }
    .file-upload { position: relative; overflow: hidden; display: inline-block; width: 100%; }
    .file-upload-name { margin-top: 5px; font-size: 0.875rem; color: #6c757d; }
    .btn-group .btn { border-radius: 0; }
    .btn-group .btn:first-child { border-top-left-radius: 0.375rem; border-bottom-left-radius: 0.375rem; }
    .btn-group .btn:last-child { border-top-right-radius: 0.375rem; border-bottom-right-radius: 0.375rem; }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
      <span class="navbar-brand"><i class="bi bi-graph-up me-2"></i>SEO Site Manager</span>
    </div>
  </nav>

  <div class="container py-4">
    <div class="row">
      <div class="col-lg-8">
        <!-- Sites Overview -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="bi bi-building me-2"></i>Your Sites</span>
            <div>
              <button class="btn btn-sm btn-outline-success me-2" onclick="showAddSiteForm();">
                <i class="bi bi-plus-circle me-1"></i>Add New Site
              </button>
              <button class="btn btn-sm btn-outline-primary" onclick="loadSites();">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
              </button>
            </div>
          </div>
          <div class="card-body">
            <div id="sitesLoading" class="text-center py-4">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              Loading your sites...
            </div>
            <div id="sitesContainer" class="d-none">
              <div id="sitesList"></div>
            </div>
            <div id="noSites" class="text-center py-4 d-none">
              <i class="bi bi-info-circle me-2 text-muted"></i>
              <span class="text-muted">No sites found. Click "Add New Site" to analyze your first website.</span>
            </div>
          </div>
        </div>

        <!-- Add New Site Form -->
        <div class="card d-none" id="addSiteCard">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="bi bi-plus-circle me-2"></i>Add New Site</span>
            <button class="btn btn-sm btn-outline-danger" onclick="hideAllCards();">
              <i class="bi bi-x-circle me-1"></i>Cancel
            </button>
          </div>
          <div class="card-body">
            <form id="analysisForm">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="domainProperty" class="form-label">Domain Property</label>
                  <input type="text" class="form-control" id="domainProperty" placeholder="https://example.com/" required>
                </div>
                <div class="col-md-6">
                  <label for="gaPropertyId" class="form-label">GA Property ID</label>
                  <input type="text" class="form-control" id="gaPropertyId" placeholder="*********" required>
                </div>
              </div>
              <div class="mb-3">
                <label for="serviceAccountFile" class="form-label">Service Account JSON</label>
                <div class="file-upload">
                  <button type="button" class="btn btn-outline-secondary w-100" onclick="document.getElementById('serviceAccountFile').click()">
                    <i class="bi bi-file-earmark-text me-2"></i>Choose Service Account File
                  </button>
                  <input type="file" class="form-control" id="serviceAccountFile" accept=".json" required style="display: none;">
                </div>
                <div class="file-upload-name" id="fileUploadName">No file chosen</div>
              </div>
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="homepage" class="form-label">Homepage URL (Optional)</label>
                  <input type="text" class="form-control" id="homepage" placeholder="https://example.com/">
                </div>
                <div class="col-md-6">
                  <label for="wpApiKey" class="form-label">WordPress API Key (Optional)</label>
                  <input type="text" class="form-control" id="wpApiKey" placeholder="Enter WordPress API key if available">
                </div>
              </div>
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="startDate" class="form-label">Start Date</label>
                  <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-6">
                  <label for="endDate" class="form-label">End Date</label>
                  <input type="date" class="form-control" id="endDate">
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary" onclick="addSiteOnly()">
                      <i class="bi bi-plus-circle me-2"></i>Add Site Only
                    </button>
                  </div>
                  <small class="text-muted mt-1 d-block">Save configuration for later analysis</small>
                </div>
                <div class="col-md-6">
                  <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                      <i class="bi bi-play-fill me-2"></i>Add Site + Analyze
                    </button>
                  </div>
                  <small class="text-muted mt-1 d-block">Save configuration and run analysis now</small>
                </div>
              </div>
            </form>
          </div>
        </div>

        <!-- Task Status -->
        <div class="card d-none" id="taskStatus">
          <div class="card-header">
            <i class="bi bi-gear-fill me-2"></i>Analysis Progress
          </div>
          <div class="card-body">
            <div class="mb-3">
              <div class="d-flex justify-content-between mb-1">
                <span>Progress:</span>
                <span id="progressPercentage">0%</span>
              </div>
              <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%"></div>
              </div>
            </div>
            <div class="mb-3">
              <p class="mb-1 fw-bold">Current Task:</p>
              <p id="currentTask">Initializing...</p>
            </div>
            <div class="d-flex justify-content-between">
              <button class="btn btn-outline-secondary" onclick="cancelTask();">
                <i class="bi bi-x-circle me-1"></i>Cancel
              </button>
              <a href="#" class="btn btn-success d-none" id="downloadBtn">
                <i class="bi bi-download me-1"></i>Download Report
              </a>
            </div>
          </div>
        </div>

        <!-- Excel Report Generation -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-file-earmark-excel me-2"></i>Generate Excel Report
          </div>
          <div class="card-body">
            <form id="excelForm">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="siteSelect" class="form-label">Select Site</label>
                  <select class="form-select" id="siteSelect" required>
                    <option value="">Choose a site...</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="reportType" class="form-label">Report Type</label>
                  <select class="form-select" id="reportType">
                    <option value="all">All Data</option>
                    <option value="date_range">Date Range</option>
                    <option value="specific_date">Specific Date</option>
                  </select>
                </div>
              </div>
              
              <div id="dateRangeOptions" class="row mb-3 d-none">
                <div class="col-md-6">
                  <label for="startDateExcel" class="form-label">Start Date</label>
                  <input type="date" class="form-control" id="startDateExcel">
                </div>
                <div class="col-md-6">
                  <label for="endDateExcel" class="form-label">End Date</label>
                  <input type="date" class="form-control" id="endDateExcel">
                </div>
              </div>
              
              <div id="specificDateOption" class="mb-3 d-none">
                <label for="specificDate" class="form-label">Specific Date</label>
                <select class="form-select" id="specificDate">
                  <option value="">Choose a date...</option>
                </select>
              </div>
              
              <div class="d-grid">
                <button type="submit" class="btn btn-success">
                  <i class="bi bi-download me-2"></i>Generate Excel Report
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-lightning me-2"></i>Quick Actions
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <button class="btn btn-outline-primary" onclick="showAddSiteForm();">
                <i class="bi bi-plus-circle me-2"></i>Add New Site
              </button>
            </div>
          </div>
        </div>

        <!-- API Documentation -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-code-square me-2"></i>API Endpoints
          </div>
          <div class="card-body">
            <h6>Site Management:</h6>
            <ul class="list-unstyled small">
              <li><code>GET /sites/</code> - List all sites</li>
              <li><code>POST /sites/</code> - Add site (config only)</li>
              <li><code>POST /sites/add-and-analyze/</code> - Add + analyze</li>
              <li><code>PUT /sites/{id}/config</code> - Update config</li>
              <li><code>DELETE /sites/{id}/data</code> - Clear data</li>
              <li><code>DELETE /sites/{id}</code> - Delete site</li>
            </ul>

            <h6>Analysis & Reports:</h6>
            <ul class="list-unstyled small">
              <li><code>POST /generate_report_with_service_account/</code> - New analysis</li>
              <li><code>POST /reanalyze_site/</code> - Quick re-analysis</li>
              <li><code>POST /generate_excel_enhanced/</code> - Excel reports</li>
              <li><code>GET /task/{task_id}</code> - Task status</li>
              <li><code>GET /download/{filename}</code> - Download files</li>
            </ul>

            <h6>Data Access:</h6>
            <ul class="list-unstyled small">
              <li><code>GET /supabase_data/{domain}</code> - Site data info</li>
            </ul>

            <div class="mt-3 pt-2 border-top">
              <small class="text-muted">All endpoints work independently of this web interface.</small>
              <br>
              <a href="/api-docs" target="_blank" class="btn btn-sm btn-outline-info mt-2">
                <i class="bi bi-book me-1"></i>View Full API Documentation
              </a>
              <a href="/docs" target="_blank" class="btn btn-sm btn-outline-secondary mt-2 ms-2">
                <i class="bi bi-code-slash me-1"></i>Swagger UI
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Configuration Modal -->
  <div class="modal fade" id="editConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Edit Site Configuration</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="editConfigForm">
            <input type="hidden" id="editSiteId">
            <div class="mb-3">
              <label for="editDomainProperty" class="form-label">Domain Property</label>
              <input type="text" class="form-control" id="editDomainProperty" placeholder="https://example.com/" required>
            </div>
            <div class="mb-3">
              <label for="editGaPropertyId" class="form-label">GA Property ID</label>
              <input type="text" class="form-control" id="editGaPropertyId" placeholder="*********" required>
            </div>
            <div class="mb-3">
              <label for="editServiceAccountFile" class="form-label">Service Account JSON (optional - leave empty to keep current)</label>
              <div class="file-upload">
                <button type="button" class="btn btn-outline-secondary w-100" onclick="document.getElementById('editServiceAccountFile').click()">
                  <i class="bi bi-file-earmark-text me-2"></i>Choose New Service Account File (Optional)
                </button>
                <input type="file" class="form-control" id="editServiceAccountFile" accept=".json" style="display: none;">
              </div>
              <div class="file-upload-name" id="editFileUploadName">No new file chosen (keeping existing)</div>
            </div>
            <div class="mb-3">
              <label for="editHomepage" class="form-label">Homepage URL (optional)</label>
              <input type="text" class="form-control" id="editHomepage" placeholder="https://example.com/">
            </div>
            <div class="mb-3">
              <label for="editWpApiKey" class="form-label">WordPress API Key (optional)</label>
              <input type="text" class="form-control" id="editWpApiKey" placeholder="Enter WordPress API key if available">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" onclick="saveConfigChanges()">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="deleteModalTitle">Confirm Action</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div id="deleteModalBody"></div>
          <div class="mt-3">
            <label for="confirmDomainInput" class="form-label">Type the domain name to confirm:</label>
            <input type="text" class="form-control" id="confirmDomainInput" placeholder="Enter domain name">
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="executeDelete()" disabled>Confirm</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    const API_ENDPOINT = 'http://localhost:8000';
    let sitesData = [];

    // Core functions
    function hideAllCards() {
      document.getElementById('addSiteCard').classList.add('d-none');
      document.getElementById('taskStatus').classList.add('d-none');
    }
    
    function showAddSiteForm() {
      hideAllCards();
      document.getElementById('addSiteCard').classList.remove('d-none');
      document.getElementById('addSiteCard').scrollIntoView({ behavior: 'smooth' });
    }

    async function loadSites() {
      try {
        document.getElementById('sitesLoading').classList.remove('d-none');
        document.getElementById('sitesContainer').classList.add('d-none');
        document.getElementById('noSites').classList.add('d-none');
        
        const response = await fetch(`${API_ENDPOINT}/sites/`);
        const data = await response.json();
        sitesData = data.sites || [];
        
        document.getElementById('sitesLoading').classList.add('d-none');
        
        if (sitesData.length === 0) {
          document.getElementById('noSites').classList.remove('d-none');
        } else {
          displaySites(sitesData);
          populateSiteSelect(sitesData);
          document.getElementById('sitesContainer').classList.remove('d-none');
        }
      } catch (error) {
        console.error('Error loading sites:', error);
        document.getElementById('sitesLoading').classList.add('d-none');
        document.getElementById('sitesContainer').innerHTML = `<div class="alert alert-danger">Error loading sites: ${error.message}</div>`;
        document.getElementById('sitesContainer').classList.remove('d-none');
      }
    }

    function displaySites(sites) {
      const sitesList = document.getElementById('sitesList');
      sitesList.innerHTML = '';
      
      sites.forEach(site => {
        const siteCard = document.createElement('div');
        siteCard.className = 'card mb-3 border-0 shadow-sm';

        // Configuration status indicator
        const configStatus = site.configuration ?
          '<span class="badge bg-success ms-2" title="Configuration stored - quick re-analysis available"><i class="bi bi-check-circle"></i> Configured</span>' :
          '<span class="badge bg-warning ms-2" title="No stored configuration - setup required for re-analysis"><i class="bi bi-exclamation-triangle"></i> Setup needed</span>';

        // Re-analyze button styling based on configuration
        const reanalyzeButtonClass = site.configuration ? 'btn-outline-warning' : 'btn-outline-secondary';
        const reanalyzeTitle = site.configuration ? 'Quick re-analysis using stored configuration' : 'Setup configuration first';

        siteCard.innerHTML = `
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-4">
                <h6 class="card-title mb-1 text-primary">
                  ${site.domain}
                  ${configStatus}
                </h6>
                <small class="text-muted">Last updated: ${site.last_updated || 'Never'}</small>
                ${site.configuration ? `<br><small class="text-info">GA: ${site.configuration.ga_property_id}</small>` : ''}
              </div>
              <div class="col-md-4">
                <div class="row text-center">
                  <div class="col"><div class="fw-bold">${site.data_summary.pages}</div><small class="text-muted">Pages</small></div>
                  <div class="col"><div class="fw-bold">${site.data_summary.keywords}</div><small class="text-muted">Keywords</small></div>
                  <div class="col"><div class="fw-bold">${site.data_summary.total_records}</div><small class="text-muted">Total</small></div>
                </div>
              </div>
              <div class="col-md-4 text-end">
                <div class="btn-group me-2" role="group">
                  <button class="btn btn-sm btn-outline-success" onclick="generateReportForSite('${site.site_id}')" title="Generate Excel Report">
                    <i class="bi bi-file-earmark-excel me-1"></i>Report
                  </button>
                  <button class="btn btn-sm ${reanalyzeButtonClass}" onclick="rerunAnalysisForSite('${site.site_id}', '${site.domain}')" title="${reanalyzeTitle}">
                    <i class="bi bi-arrow-repeat me-1"></i>Re-analyze
                  </button>
                </div>
                <div class="btn-group" role="group">
                  <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" title="Site Management">
                    <i class="bi bi-gear"></i>
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="editSiteConfig('${site.site_id}', '${site.domain}')">
                      <i class="bi bi-pencil me-2"></i>Edit Configuration
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-warning" href="#" onclick="clearSiteData('${site.site_id}', '${site.domain}')">
                      <i class="bi bi-trash me-2"></i>Clear Data
                    </a></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteSiteCompletely('${site.site_id}', '${site.domain}')">
                      <i class="bi bi-trash-fill me-2"></i>Delete Site
                    </a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        `;
        sitesList.appendChild(siteCard);
      });
    }

    function populateSiteSelect(sites) {
      const siteSelect = document.getElementById('siteSelect');
      siteSelect.innerHTML = '<option value="">Choose a site...</option>';
      sites.forEach(site => {
        const option = document.createElement('option');
        option.value = site.site_id;
        option.textContent = `${site.domain} (${site.data_summary.total_records} records)`;
        option.dataset.domain = site.domain;
        option.dataset.dates = JSON.stringify(site.available_dates);
        siteSelect.appendChild(option);
      });
    }

    function generateReportForSite(siteId) {
      document.getElementById('siteSelect').value = siteId;
      document.getElementById('reportType').value = 'all';
      document.getElementById('excelForm').scrollIntoView({ behavior: 'smooth' });
    }

    function rerunAnalysisForSite(siteId, domain) {
      // Find the site data to check if it has configuration
      const site = sitesData.find(s => s.site_id == siteId);

      if (site && site.configuration) {
        // Site has stored configuration - use simple re-analysis
        const confirmed = confirm(`Re-run analysis for ${domain}?\n\nThis will update the site data with fresh information using the stored configuration (GA Property: ${site.configuration.ga_property_id}).`);

        if (!confirmed) return;

        // Optional: Ask for date range
        const useCustomDates = confirm('Use custom date range? (Click Cancel to use default dates)');
        let startDate = null;
        let endDate = null;

        if (useCustomDates) {
          startDate = prompt('Start date (YYYY-MM-DD):');
          endDate = prompt('End date (YYYY-MM-DD):');

          // Validate dates
          if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
            alert('Invalid start date format. Use YYYY-MM-DD');
            return;
          }
          if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
            alert('Invalid end date format. Use YYYY-MM-DD');
            return;
          }
        }

        // Prepare the request data
        const requestData = {
          site_id: siteId,
          start_date: startDate,
          end_date: endDate
        };

        // Show task status
        hideAllCards();
        document.getElementById('taskStatus').classList.remove('d-none');
        document.getElementById('currentTask').textContent = `Re-analyzing ${domain}...`;
        document.getElementById('progressBar').style.width = '0%';
        document.getElementById('progressPercentage').textContent = '0%';

        // Submit the re-analysis request using stored configuration
        fetch(`${API_ENDPOINT}/reanalyze_site/`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData)
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          pollTaskStatus(result.task_id);
        })
        .catch(error => {
          alert('Error starting re-analysis: ' + error.message);
          hideAllCards();
        });

      } else {
        // Site doesn't have stored configuration - need to provide details
        alert(`Site ${domain} doesn't have stored configuration.\n\nPlease use "Add New Site" to set up the configuration first, then you can use quick re-analysis.`);
      }
    }

    function updateDateOptions() {
      const reportType = document.getElementById('reportType').value;
      const dateRangeOptions = document.getElementById('dateRangeOptions');
      const specificDateOption = document.getElementById('specificDateOption');
      
      dateRangeOptions.classList.add('d-none');
      specificDateOption.classList.add('d-none');
      
      if (reportType === 'date_range') {
        dateRangeOptions.classList.remove('d-none');
      } else if (reportType === 'specific_date') {
        const selectedOption = document.getElementById('siteSelect').options[document.getElementById('siteSelect').selectedIndex];
        if (selectedOption && selectedOption.dataset.dates) {
          const availableDates = JSON.parse(selectedOption.dataset.dates);
          const specificDate = document.getElementById('specificDate');
          specificDate.innerHTML = '<option value="">Choose a date...</option>';
          availableDates.forEach(date => {
            const option = document.createElement('option');
            option.value = date;
            option.textContent = date;
            specificDate.appendChild(option);
          });
          specificDateOption.classList.remove('d-none');
        }
      }
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // File upload
      document.getElementById('serviceAccountFile').addEventListener('change', function(e) {
        document.getElementById('fileUploadName').textContent = e.target.files[0]?.name || 'No file chosen';
      });

      // Report type change
      document.getElementById('reportType').addEventListener('change', updateDateOptions);
      document.getElementById('siteSelect').addEventListener('change', updateDateOptions);

      // Analysis form (Add Site + Analyze)
      document.getElementById('analysisForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await addSiteAndAnalyze();
      });

      // Excel form
      document.getElementById('excelForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const siteId = document.getElementById('siteSelect').value;
        if (!siteId) {
          alert('Please select a site');
          return;
        }

        const requestData = { site_id: siteId };
        const reportType = document.getElementById('reportType').value;
        
        if (reportType === 'date_range') {
          const startDate = document.getElementById('startDateExcel').value;
          const endDate = document.getElementById('endDateExcel').value;
          if (startDate) requestData.start_date = startDate;
          if (endDate) requestData.end_date = endDate;
        } else if (reportType === 'specific_date') {
          const specificDate = document.getElementById('specificDate').value;
          if (specificDate) requestData.date = specificDate;
        }

        try {
          const response = await fetch(`${API_ENDPOINT}/generate_excel_enhanced/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
          });

          const result = await response.json();
          document.getElementById('taskStatus').classList.remove('d-none');
          pollTaskStatus(result.task_id);
          
        } catch (error) {
          alert('Error: ' + error.message);
        }
      });

      // Set default dates
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);
      
      document.getElementById('startDate').value = oneYearAgo.toISOString().split('T')[0];
      document.getElementById('endDate').value = today.toISOString().split('T')[0];

      // Edit config file upload
      document.getElementById('editServiceAccountFile').addEventListener('change', function(e) {
        document.getElementById('editFileUploadName').textContent = e.target.files[0]?.name || 'No new file chosen (keeping existing)';
      });

      // Domain confirmation for delete
      document.getElementById('confirmDomainInput').addEventListener('input', function(e) {
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        if (currentSiteForAction && e.target.value.toLowerCase() === currentSiteForAction.domain.toLowerCase()) {
          confirmBtn.disabled = false;
        } else {
          confirmBtn.disabled = true;
        }
      });

      // Load sites on page load
      loadSites();
    });

    async function pollTaskStatus(taskId) {
      try {
        const response = await fetch(`${API_ENDPOINT}/task/${taskId}`);
        const status = await response.json();
        
        document.getElementById('progressPercentage').textContent = `${status.progress || 0}%`;
        document.getElementById('progressBar').style.width = `${status.progress || 0}%`;
        document.getElementById('currentTask').textContent = status.message || 'Processing...';
        
        if (status.status === 'completed') {
          document.getElementById('progressBar').classList.remove('progress-bar-animated');
          document.getElementById('currentTask').textContent = 'Analysis completed!';
          
          if (status.result && status.result.excel_report) {
            const downloadBtn = document.getElementById('downloadBtn');
            downloadBtn.href = `${API_ENDPOINT}/download/${encodeURIComponent(status.result.excel_report)}`;
            downloadBtn.classList.remove('d-none');
          }
          
          setTimeout(() => {
            loadSites(); // Refresh sites list
          }, 2000);
          
        } else if (status.status === 'failed') {
          document.getElementById('currentTask').textContent = `Error: ${status.error || 'Analysis failed'}`;
          document.getElementById('progressBar').classList.remove('progress-bar-animated');
        } else {
          setTimeout(() => pollTaskStatus(taskId), 2000);
        }
      } catch (error) {
        console.error('Error polling task status:', error);
      }
    }

    function cancelTask() {
      hideAllCards();
    }

    // Site Addition Functions
    async function addSiteOnly() {
      try {
        const formData = await getFormData();
        if (!formData) return;

        const requestData = {
          domain_property: formData.domain_property,
          ga_property_id: formData.ga_property_id,
          service_account_data: formData.service_account_data,
          homepage: formData.homepage,
          wp_api_key: formData.wp_api_key
        };

        const response = await fetch(`${API_ENDPOINT}/sites/`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData)
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to add site');
        }

        const result = await response.json();

        hideAllCards();
        alert(`Site ${result.domain} added successfully!\n\nYou can now run analysis using the "Re-analyze" button.`);
        loadSites(); // Refresh sites list

      } catch (error) {
        alert('Error adding site: ' + error.message);
      }
    }

    async function addSiteAndAnalyze() {
      try {
        const formData = await getFormData();
        if (!formData) return;

        const requestData = {
          domain_property: formData.domain_property,
          ga_property_id: formData.ga_property_id,
          service_account_data: formData.service_account_data,
          homepage: formData.homepage,
          wp_api_key: formData.wp_api_key,
          start_date: formData.start_date,
          end_date: formData.end_date
        };

        hideAllCards();
        document.getElementById('taskStatus').classList.remove('d-none');
        document.getElementById('currentTask').textContent = 'Adding site and starting analysis...';
        document.getElementById('progressBar').style.width = '0%';
        document.getElementById('progressPercentage').textContent = '0%';

        const response = await fetch(`${API_ENDPOINT}/sites/add-and-analyze/`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData)
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to add site and start analysis');
        }

        const result = await response.json();
        pollTaskStatus(result.task_id);

      } catch (error) {
        alert('Error: ' + error.message);
        hideAllCards();
      }
    }

    async function getFormData() {
      const serviceAccountFile = document.getElementById('serviceAccountFile').files[0];
      if (!serviceAccountFile) {
        alert('Please select a service account file');
        return null;
      }

      const domainProperty = document.getElementById('domainProperty').value;
      const gaPropertyId = document.getElementById('gaPropertyId').value;

      if (!domainProperty || !gaPropertyId) {
        alert('Please fill in all required fields');
        return null;
      }

      return new Promise((resolve, reject) => {
        const fileReader = new FileReader();
        fileReader.onload = function(event) {
          try {
            const serviceAccount = JSON.parse(event.target.result);

            const homepage = document.getElementById('homepage').value || domainProperty;
            const wpApiKey = document.getElementById('wpApiKey').value || null;

            resolve({
              domain_property: domainProperty,
              ga_property_id: gaPropertyId,
              service_account_data: serviceAccount,
              homepage: homepage,
              wp_api_key: wpApiKey,
              start_date: document.getElementById('startDate').value || null,
              end_date: document.getElementById('endDate').value || null
            });
          } catch (error) {
            reject(new Error('Invalid JSON file'));
          }
        };
        fileReader.onerror = () => reject(new Error('Error reading file'));
        fileReader.readAsText(serviceAccountFile);
      });
    }

    // Site Management Functions
    let currentSiteForAction = null;
    let currentActionType = null;

    function editSiteConfig(siteId, domain) {
      currentSiteForAction = { siteId, domain };

      // Find the site data
      const site = sitesData.find(s => s.site_id == siteId);

      // Pre-fill form if configuration exists
      if (site && site.configuration) {
        document.getElementById('editDomainProperty').value = site.configuration.domain_property || '';
        document.getElementById('editGaPropertyId').value = site.configuration.ga_property_id || '';
        document.getElementById('editHomepage').value = site.configuration.homepage || '';
        document.getElementById('editWpApiKey').value = site.configuration.wp_api_key || '';
      } else {
        // Clear form for new configuration
        document.getElementById('editDomainProperty').value = `https://${domain}/`;
        document.getElementById('editGaPropertyId').value = '';
        document.getElementById('editHomepage').value = `https://${domain}/`;
        document.getElementById('editWpApiKey').value = '';
      }

      document.getElementById('editSiteId').value = siteId;
      document.getElementById('editFileUploadName').textContent = 'No new file chosen (keeping existing)';

      // Show modal
      new bootstrap.Modal(document.getElementById('editConfigModal')).show();
    }

    function clearSiteData(siteId, domain) {
      currentSiteForAction = { siteId, domain };
      currentActionType = 'clear';

      document.getElementById('deleteModalTitle').textContent = 'Clear Site Data';
      document.getElementById('deleteModalBody').innerHTML = `
        <div class="alert alert-warning">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <strong>Warning:</strong> This will delete all analysis data for <strong>${domain}</strong> but keep the site in your list.
        </div>
        <p>This action will remove:</p>
        <ul>
          <li>All page data</li>
          <li>All keyword data</li>
          <li>All traffic data</li>
          <li>All internal links data</li>
          <li>All analytics data</li>
          <li>Site configuration</li>
        </ul>
        <p><strong>The site will remain in your list but will need to be re-analyzed.</strong></p>
      `;

      document.getElementById('confirmDomainInput').value = '';
      document.getElementById('confirmDeleteBtn').disabled = true;

      new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
    }

    function deleteSiteCompletely(siteId, domain) {
      currentSiteForAction = { siteId, domain };
      currentActionType = 'delete';

      document.getElementById('deleteModalTitle').textContent = 'Delete Site Completely';
      document.getElementById('deleteModalBody').innerHTML = `
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <strong>Danger:</strong> This will permanently delete <strong>${domain}</strong> and all its data.
        </div>
        <p>This action will remove:</p>
        <ul>
          <li>The site from your sites list</li>
          <li>All page data</li>
          <li>All keyword data</li>
          <li>All traffic data</li>
          <li>All internal links data</li>
          <li>All analytics data</li>
          <li>Site configuration</li>
        </ul>
        <p><strong>This action cannot be undone!</strong></p>
      `;

      document.getElementById('confirmDomainInput').value = '';
      document.getElementById('confirmDeleteBtn').disabled = true;

      new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
    }

    async function saveConfigChanges() {
      const siteId = document.getElementById('editSiteId').value;
      const domainProperty = document.getElementById('editDomainProperty').value;
      const gaPropertyId = document.getElementById('editGaPropertyId').value;
      const homepage = document.getElementById('editHomepage').value;
      const wpApiKey = document.getElementById('editWpApiKey').value;
      const serviceAccountFile = document.getElementById('editServiceAccountFile').files[0];

      if (!domainProperty || !gaPropertyId) {
        alert('Domain Property and GA Property ID are required');
        return;
      }

      try {
        let serviceAccountData = null;

        // If a new service account file is provided, read it
        if (serviceAccountFile) {
          const fileReader = new FileReader();
          serviceAccountData = await new Promise((resolve, reject) => {
            fileReader.onload = function(event) {
              try {
                resolve(JSON.parse(event.target.result));
              } catch (error) {
                reject(new Error('Invalid JSON file'));
              }
            };
            fileReader.onerror = () => reject(new Error('Error reading file'));
            fileReader.readAsText(serviceAccountFile);
          });
        } else {
          // Use existing service account data
          const site = sitesData.find(s => s.site_id == siteId);
          if (!site || !site.configuration || !site.configuration.service_account_data) {
            alert('No existing service account data found. Please provide a service account file.');
            return;
          }
          // We'll need to get the actual service account data from the backend
          // For now, we'll send a placeholder and handle it on the backend
          serviceAccountData = { _keep_existing: true };
        }

        const requestData = {
          site_id: siteId,
          domain_property: domainProperty,
          ga_property_id: gaPropertyId,
          service_account_data: serviceAccountData,
          homepage: homepage || null,
          wp_api_key: wpApiKey || null
        };

        const response = await fetch(`${API_ENDPOINT}/sites/${siteId}/config`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData)
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to update configuration');
        }

        const result = await response.json();

        // Close modal and refresh sites
        bootstrap.Modal.getInstance(document.getElementById('editConfigModal')).hide();
        alert(`Configuration updated successfully for ${result.domain}`);
        loadSites();

      } catch (error) {
        alert('Error updating configuration: ' + error.message);
      }
    }

    async function executeDelete() {
      if (!currentSiteForAction) return;

      const confirmDomain = document.getElementById('confirmDomainInput').value;
      if (confirmDomain.toLowerCase() !== currentSiteForAction.domain.toLowerCase()) {
        alert('Domain confirmation does not match');
        return;
      }

      try {
        const endpoint = currentActionType === 'clear'
          ? `/sites/${currentSiteForAction.siteId}/data`
          : `/sites/${currentSiteForAction.siteId}`;

        const requestData = {
          site_id: currentSiteForAction.siteId,
          confirm_domain: currentSiteForAction.domain,
          delete_all_data: true
        };

        const response = await fetch(`${API_ENDPOINT}${endpoint}`, {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData)
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.detail || 'Failed to delete');
        }

        const result = await response.json();

        // Close modal and refresh sites
        bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
        alert(result.message);
        loadSites();

      } catch (error) {
        alert('Error: ' + error.message);
      }
    }
  </script>
</body>
</html>
