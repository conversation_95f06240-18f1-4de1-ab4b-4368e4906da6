"""
Supabase database client
"""
import hashlib
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from src.utils.logging import get_logger

logger = get_logger(__name__)

try:
    from supabase import create_client
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase client not available. Install with: pip install supabase")
    SUPABASE_AVAILABLE = False


class SupabaseClient:
    """Client for Supabase database operations"""
    
    def __init__(self, url: str, key: str, domain: str):
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase client not available")
        
        self.client = create_client(url, key)
        self.domain = domain
        self.site_id = self._get_or_create_site_id()
        self.db_id = self.site_id
    
    def _get_or_create_site_id(self) -> str:
        """Get or create a site ID for the domain"""
        # Check if site exists
        response = self.client.table('sites').select('id').eq('domain', self.domain).execute()

        if response.data:
            return response.data[0]['id']

        # Create new site - let database auto-generate the ID
        site_data = {
            'domain': self.domain,
            'created_at': datetime.now().isoformat()
        }

        response = self.client.table('sites').insert(site_data).execute()

        if response.data and len(response.data) > 0:
            return response.data[0]['id']
        else:
            raise Exception(f"Failed to create site for domain {self.domain}")

    def save_site_configuration(self, domain_property: str, ga_property_id: str,
                               service_account_data: dict, homepage: str = None) -> tuple[bool, str]:
        """Save site configuration for future re-analysis"""
        try:
            config_data = {
                'domain_property': domain_property,
                'ga_property_id': ga_property_id,
                'service_account_data': service_account_data,
                'homepage': homepage,
                'last_updated': datetime.now().isoformat()
            }

            logger.info(f"Attempting to save configuration for site {self.domain} (ID: {self.site_id})")

            # Update the site record with configuration
            response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()

            if response.data is not None and len(response.data) > 0:
                logger.info(f"Successfully saved configuration for site {self.domain}")
                return True, "Configuration saved successfully"
            elif response.data is not None and len(response.data) == 0:
                logger.error(f"No rows updated for site {self.domain} - site may not exist")
                return False, f"Site with ID {self.site_id} not found"
            else:
                logger.error(f"Unexpected response from Supabase: {response}")
                return False, "Unexpected response from database"

        except Exception as e:
            error_msg = f"Error saving site configuration: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full exception details:")
            return False, error_msg

    @classmethod
    def create_site_with_config(cls, url: str, key: str, domain_property: str,
                               ga_property_id: str, service_account_data: dict,
                               homepage: str = None) -> tuple[bool, str, str]:
        """Create a new site with configuration (no analysis)"""
        try:
            from urllib.parse import urlparse
            from supabase import create_client

            domain = urlparse(domain_property).netloc

            # Create direct client connection (don't use SupabaseClient constructor)
            client = create_client(url, key)

            # Check if site already exists
            existing_response = client.table('sites').select('id, domain').eq('domain', domain).execute()

            if existing_response.data:
                return False, f"Site {domain} already exists", str(existing_response.data[0]['id'])

            # Create site with configuration - let database auto-generate ID
            site_data = {
                'domain': domain,
                'domain_property': domain_property,
                'ga_property_id': ga_property_id,
                'service_account_data': service_account_data,
                'homepage': homepage,
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }

            response = client.table('sites').insert(site_data).execute()

            if response.data and len(response.data) > 0:
                site_id = str(response.data[0]['id'])
                logger.info(f"Created site {domain} with configuration (ID: {site_id})")
                return True, f"Site {domain} created successfully", site_id
            else:
                logger.error(f"Failed to create site {domain}")
                return False, f"Failed to create site {domain}", None

        except Exception as e:
            logger.error(f"Error creating site with configuration: {e}")
            return False, f"Error creating site: {str(e)}", None

    def get_site_configuration(self) -> Optional[dict]:
        """Get stored site configuration"""
        try:
            response = self.client.table('sites').select('domain_property, ga_property_id, service_account_data, homepage').eq('id', self.site_id).execute()

            if response.data and response.data[0].get('domain_property'):
                config = response.data[0]
                logger.info(f"Retrieved configuration for site {self.domain}")
                return config
            else:
                logger.warning(f"No configuration found for site {self.domain}")
                return None

        except Exception as e:
            logger.error(f"Error retrieving site configuration: {e}")
            return None

    def update_site_configuration(self, domain_property: str, ga_property_id: str,
                                 service_account_data: dict, homepage: str = None) -> tuple[bool, str]:
        """Update existing site configuration"""
        try:
            config_data = {
                'domain_property': domain_property,
                'ga_property_id': ga_property_id,
                'service_account_data': service_account_data,
                'homepage': homepage,
                'last_updated': datetime.now().isoformat()
            }

            logger.info(f"Attempting to update configuration for site {self.domain} (ID: {self.site_id})")
            logger.debug(f"Update data: {config_data}")

            # Update the site record with new configuration
            response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()

            logger.debug(f"Supabase response: {response}")

            # Check if the update was successful
            if response.data is not None and len(response.data) > 0:
                logger.info(f"Successfully updated configuration for site {self.domain}")
                return True, "Configuration updated successfully"
            elif response.data is not None and len(response.data) == 0:
                logger.error(f"No rows updated for site {self.domain} - site may not exist")
                return False, f"Site with ID {self.site_id} not found or no changes made"
            else:
                logger.error(f"Unexpected response from Supabase: {response}")
                return False, "Unexpected response from database"

        except Exception as e:
            error_msg = f"Error updating site configuration: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full exception details:")
            return False, error_msg

    def delete_all_site_data(self) -> bool:
        """Delete all data for this site from all tables"""
        try:
            tables_to_clean = ['pages', 'gsc_keywords', 'gsc_traffic', 'internal_links', 'ga_data']
            deleted_counts = {}

            for table in tables_to_clean:
                try:
                    # Get count before deletion
                    count_response = self.client.table(table).select('id').eq('site_id', self.site_id).execute()
                    count = len(count_response.data) if count_response.data else 0

                    if count > 0:
                        # Delete all records for this site
                        delete_response = self.client.table(table).delete().eq('site_id', self.site_id).execute()
                        deleted_counts[table] = count
                        logger.info(f"Deleted {count} records from {table} for site {self.domain}")
                    else:
                        deleted_counts[table] = 0

                except Exception as table_error:
                    logger.error(f"Error deleting from table {table}: {table_error}")
                    deleted_counts[table] = f"Error: {table_error}"

            # Update site record to clear configuration but keep the site entry
            try:
                self.client.table('sites').update({
                    'domain_property': None,
                    'ga_property_id': None,
                    'service_account_data': None,
                    'homepage': None,
                    'last_updated': datetime.now().isoformat()
                }).eq('id', self.site_id).execute()
                logger.info(f"Cleared configuration for site {self.domain}")
            except Exception as config_error:
                logger.error(f"Error clearing site configuration: {config_error}")

            logger.info(f"Site data deletion summary for {self.domain}: {deleted_counts}")
            return True

        except Exception as e:
            logger.error(f"Error deleting site data: {e}")
            return False

    def delete_site_completely(self) -> bool:
        """Delete site and all its data completely"""
        try:
            # First delete all data
            if not self.delete_all_site_data():
                logger.error(f"Failed to delete site data for {self.domain}")
                return False

            # Then delete the site record itself
            response = self.client.table('sites').delete().eq('id', self.site_id).execute()

            if response.data is not None:  # Supabase returns empty list for successful deletes
                logger.info(f"Completely deleted site {self.domain}")
                return True
            else:
                logger.error(f"Failed to delete site record for {self.domain}")
                return False

        except Exception as e:
            logger.error(f"Error completely deleting site: {e}")
            return False
    
    def save_pages_data(self, df: pd.DataFrame) -> Optional[Any]:
        """Save pages data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')

        # Add URL hash if it doesn't exist
        if 'url_hash' not in df.columns:
            df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('pages').upsert(
                records,
                on_conflict='site_id,url_hash,snapshot_date'
            ).execute()

            logger.info(f"Saved {len(records)} pages to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving pages data: {e}")
            return None
    
    def save_gsc_keywords(self, df: pd.DataFrame) -> Optional[Any]:
        """Save GSC keywords data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id

        # Add keyword hash
        if 'keyword_hash' not in df.columns:
            df['keyword_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('Keyword', '')}{row.get('URL', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('gsc_keywords').upsert(
                records,
                on_conflict='site_id,keyword_hash'
            ).execute()

            logger.info(f"Saved {len(records)} GSC keywords to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GSC keywords: {e}")
            return None
    
    def save_gsc_traffic(self, df: pd.DataFrame) -> Optional[Any]:
        """Save GSC traffic data to Supabase"""
        if df.empty or self.db_id is None:
            return None
        
        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id
        
        # Add traffic hash
        if 'traffic_hash' not in df.columns:
            df['traffic_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('URL', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )
        
        # Replace NaN values with None
        df = df.replace({float('nan'): None})
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        try:
            response = self.client.table('gsc_traffic').upsert(
                records,
                on_conflict='site_id,traffic_hash'
            ).execute()
            
            logger.info(f"Saved {len(records)} GSC traffic records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GSC traffic: {e}")
            return None

    def save_internal_links(self, df: pd.DataFrame) -> Optional[Any]:
        """Save internal links data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id and snapshot date
        df = df.copy()
        df['site_id'] = self.db_id
        df['snapshot_date'] = datetime.now().strftime('%Y-%m-%d')

        # Add link_hash if it doesn't exist
        if 'link_hash' not in df.columns:
            df['link_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row['URL']}|{row['Target Hyperlink']}".encode()).hexdigest(),
                axis=1
            )

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('internal_links').upsert(
                records,
                on_conflict='site_id,link_hash,snapshot_date'
            ).execute()

            logger.info(f"Saved {len(records)} internal links to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving internal links: {e}")
            return None

    def save_ga_data(self, df: pd.DataFrame) -> Optional[Any]:
        """Save Google Analytics data to Supabase"""
        if df.empty or self.db_id is None:
            return None

        # Add site_id
        df = df.copy()
        df['site_id'] = self.db_id

        # Add GA hash
        if 'ga_hash' not in df.columns:
            df['ga_hash'] = df.apply(
                lambda row: hashlib.md5(f"{row.get('pagePath', '')}{row.get('Month', '')}".encode()).hexdigest(),
                axis=1
            )

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        try:
            response = self.client.table('ga_data').upsert(
                records,
                on_conflict='site_id,ga_hash'
            ).execute()

            logger.info(f"Saved {len(records)} GA records to Supabase")
            return response.data
        except Exception as e:
            logger.error(f"Error saving GA data: {e}")
            return None

    def get_pages_data(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve pages data from Supabase"""
        try:
            query = self.client.table('pages').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving pages data: {e}")
            return pd.DataFrame()

    def get_gsc_keywords(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC keywords data from Supabase"""
        try:
            query = self.client.table('gsc_keywords').select('*').eq('site_id', self.site_id)
            if date_filter:
                # For keywords, we might want to filter by month
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC keywords: {e}")
            return pd.DataFrame()

    def get_gsc_traffic(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve GSC traffic data from Supabase"""
        try:
            query = self.client.table('gsc_traffic').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GSC traffic: {e}")
            return pd.DataFrame()

    def get_internal_links(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve internal links data from Supabase"""
        try:
            query = self.client.table('internal_links').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('snapshot_date', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving internal links: {e}")
            return pd.DataFrame()

    def get_ga_data(self, date_filter: Optional[str] = None) -> pd.DataFrame:
        """Retrieve Google Analytics data from Supabase"""
        try:
            query = self.client.table('ga_data').select('*').eq('site_id', self.site_id)
            if date_filter:
                query = query.eq('Month', date_filter)

            response = query.execute()
            if response.data:
                return pd.DataFrame(response.data)
            return pd.DataFrame()
        except Exception as e:
            logger.error(f"Error retrieving GA data: {e}")
            return pd.DataFrame()

    def generate_excel_report(self, output_dir: str, date_filter: Optional[str] = None,
                            include_raw_data: bool = True, include_keywords: bool = True,
                            include_traffic: bool = True, include_links: bool = True,
                            include_analytics: bool = True) -> str:
        """Generate Excel report from Supabase data - matches original main.py format"""
        from datetime import datetime
        import os

        logger.info(f"Generating Excel report from Supabase data for domain: {self.domain}")

        # Create Excel file path (match original format)
        date_str = f"_{datetime.now().strftime('%Y%m%d')}"
        excel_path = os.path.join(output_dir, f'report_{self.domain.replace(".", "_")}{date_str}.xlsx')

        # Use xlsxwriter engine to match original format
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            # Pages data sheet (match original 'Data' sheet with ALL columns)
            if include_raw_data:
                pages_df = self.get_pages_data(date_filter)
                if not pages_df.empty:
                    # Include ALL columns like original main.py
                    pages_df.to_excel(writer, sheet_name='Data', index=False)
                    logger.info(f"Added {len(pages_df)} pages to Excel report")
                else:
                    # Create empty sheet with headers if no data
                    empty_df = pd.DataFrame(columns=['url', 'title', 'description', 'h1', 'text', 'snapshot_date'])
                    empty_df.to_excel(writer, sheet_name='Data', index=False)
                    logger.info("Added empty Data sheet (no pages data)")

            # Keywords sheet (match original 'Keywords' sheet with ALL columns)
            if include_keywords:
                keywords_df = self.get_gsc_keywords(date_filter)
                if not keywords_df.empty:
                    # Include ALL columns like original main.py
                    keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                    logger.info(f"Added {len(keywords_df)} keywords to Excel report")
                else:
                    # Create empty sheet with headers if no data
                    empty_df = pd.DataFrame(columns=['query', 'page', 'clicks', 'impressions', 'ctr', 'position', 'Month'])
                    empty_df.to_excel(writer, sheet_name='Keywords', index=False)
                    logger.info("Added empty Keywords sheet (no keywords data)")

            # Traffic sheet (match original 'Historical-Traffic' sheet with ALL columns)
            if include_traffic:
                traffic_df = self.get_gsc_traffic(date_filter)
                if not traffic_df.empty:
                    # Include ALL columns like original main.py
                    traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                    logger.info(f"Added {len(traffic_df)} traffic records to Excel report")
                else:
                    # Create empty sheet with headers if no data
                    empty_df = pd.DataFrame(columns=['page', 'clicks', 'impressions', 'ctr', 'position', 'Month'])
                    empty_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                    logger.info("Added empty Historical-Traffic sheet (no traffic data)")

            # Internal links sheet (match original 'Internal-Links' sheet with ALL columns)
            if include_links:
                links_df = self.get_internal_links(date_filter)
                if not links_df.empty:
                    # Include ALL columns like original main.py
                    links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
                    logger.info(f"Added {len(links_df)} internal links to Excel report")
                else:
                    # Create empty sheet with headers if no data (this is the missing sheet!)
                    empty_df = pd.DataFrame(columns=['URL', 'Target Hyperlink', 'Anchor Text', 'Link Type', 'URL Topic', 'Target Title', 'Relevance Score'])
                    empty_df.to_excel(writer, sheet_name='Internal-Links', index=False)
                    logger.info("Added empty Internal-Links sheet (no internal links data)")

            # Google Analytics sheet (match original 'GA-Data' sheet with ALL columns)
            if include_analytics:
                ga_df = self.get_ga_data(date_filter)
                if not ga_df.empty:
                    # Include ALL columns like original main.py
                    ga_df.to_excel(writer, sheet_name='GA-Data', index=False)
                    logger.info(f"Added {len(ga_df)} GA records to Excel report")
                else:
                    # Create empty sheet with headers if no data
                    empty_df = pd.DataFrame(columns=['pagePath', 'sessions', 'pageviews', 'bounceRate', 'avgSessionDuration', 'Month'])
                    empty_df.to_excel(writer, sheet_name='GA-Data', index=False)
                    logger.info("Added empty GA-Data sheet (no analytics data)")

        logger.info(f"Excel report generated from Supabase data: {excel_path}")
        return excel_path
