"""
WordPress API integration
"""
import requests
from typing import Optional, Dict, Any
from urllib.parse import urlparse

from src.utils.logging import get_logger

logger = get_logger(__name__)


class WordPressAPIClient:
    """Client for WordPress Data Exporter API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    def detect_wp_api(self, domain: str) -> Optional[str]:
        """Detect WordPress API endpoint for a domain"""
        parsed_domain = urlparse(domain)
        base_url = f"{parsed_domain.scheme}://{parsed_domain.netloc}"
        
        # Common WordPress API endpoints to try
        endpoints = [
            f"{base_url}/wp-json/wp-data-exporter/v1/data",  # Your specific endpoint
       
        ]
        
        for endpoint in endpoints:
            try:
                logger.info(f"Trying WordPress API endpoint: {endpoint}")
                response = requests.head(endpoint, timeout=10)
                logger.info(f"Response status for {endpoint}: {response.status_code}")
                if response.status_code in [200, 405]:  # 405 is method not allowed but endpoint exists
                    logger.info(f"Found WordPress API endpoint: {endpoint}")
                    return endpoint
            except requests.RequestException as e:
                logger.info(f"Failed to connect to {endpoint}: {e}")
                continue
        
        logger.warning(f"No WordPress API endpoint found for {domain}")
        return None
    
    def fetch_data(self, wp_api_url: str) -> Optional[Dict[str, Any]]:
        """Fetch data from WordPress API"""
        logger.info(f"Fetching data from WordPress API: {wp_api_url}")
        headers = {'X-Plugin-API-Key': self.api_key}
        logger.info(f"Using API key: {self.api_key[:10]}..." if self.api_key else "No API key provided")

        try:
            response = requests.get(wp_api_url, headers=headers, timeout=60)
            logger.info(f"WordPress API response status: {response.status_code}")
            response.raise_for_status()
            data = response.json()
            logger.info(f"WordPress API response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            if isinstance(data, dict) and 'internal_links_data' in data:
                logger.info(f"Found {len(data['internal_links_data'])} internal links in WordPress API response")
            logger.info("Successfully fetched data from WordPress API.")
            return data
        except requests.exceptions.RequestException as e:
            logger.error(f"Could not fetch data from WordPress API: {e}")
            return None
