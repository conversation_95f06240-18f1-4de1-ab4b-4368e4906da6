2025-06-30 03:49:18,264 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:49:33,807 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:49:33,808 - src.cli.main - INFO - Starting API server on 0.0.0.0:8000
2025-06-30 03:50:01,889 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 03:50:01,890 - src.cli.main - INFO - Starting API server on 0.0.0.0:8001
2025-06-30 04:09:00,594 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:09:00,595 - src.cli.main - INFO - Starting API server on 0.0.0.0:8001
2025-06-30 04:10:37,429 - src.api.routes - ERROR - Error generating Excel from Supabase for task 713ec8d3-8d06-482a-aaa5-b11346b7aa54
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
        pool_request.request
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 101, in handle_request
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 78, in handle_request
    stream = self._connect(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_backends\sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 80, in generate_excel_from_supabase
    supabase_client = SupabaseClient(
        url=supabase_url,
        key=supabase_key,
        domain=domain
    )
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 31, in __init__
    self.site_id = self._get_or_create_site_id()
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 39, in _get_or_create_site_id
    response = self.client.table('sites').select('id').eq('domain', self.domain).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 57, in execute
    r = self.session.request(
        self.http_method,
    ...<3 lines>...
        headers=self.headers,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
        request,
    ...<2 lines>...
        history=[],
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
        request,
        follow_redirects=follow_redirects,
        history=history,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-30 04:10:48,466 - src.services.analysis_service - ERROR - Error in SEO analysis for task 5618550b-d9ad-4f67-abbe-5a360b14e8be
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 58, in run_analysis
    supabase_url = config['supabase_url']  # Required field
                   ~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'supabase_url'
2025-06-30 04:10:48,468 - src.api.routes - ERROR - Error in background analysis task 5618550b-d9ad-4f67-abbe-5a360b14e8be
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 51, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 58, in run_analysis
    supabase_url = config['supabase_url']  # Required field
                   ~~~~~~^^^^^^^^^^^^^^^^
KeyError: 'supabase_url'
2025-06-30 04:22:37,179 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:22:37,180 - __main__ - INFO - Starting API server on 0.0.0.0:8000
2025-06-30 04:23:02,182 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:23:29,975 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:23:46,678 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:27:33,757 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:28:15,693 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:30:33,754 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:45:17,449 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:45:27,017 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:45:27,747 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:28,451 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:28,725 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:28,966 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,227 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,464 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,696 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:29,950 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:30,231 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:45:30,466 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:26,303 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:46:26,537 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:26,776 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,029 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,266 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,508 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,747 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:27,985 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:28,237 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:28,488 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:28,737 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:46:44,944 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 04:47:01,576 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:47:01,849 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,109 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,387 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,635 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:02,898 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,135 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,405 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,676 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:03,919 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:04,167 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:12,322 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 04:47:12,801 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:13,153 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:13,507 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 04:47:13,879 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:00:38,288 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:00:38,325 - src.api.routes - ERROR - Error listing sites:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
        pool_request.request
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\connection.py", line 103, in handle_request
    return self._connection.handle_request(request)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 187, in handle_request
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 150, in handle_request
    status, headers = self._receive_response(
                      ~~~~~~~~~~~~~~~~~~~~~~^
        request=request, stream_id=stream_id
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 294, in _receive_response
    event = self._receive_stream_event(request, stream_id)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 336, in _receive_stream_event
    self._receive_events(request, stream_id)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 364, in _receive_events
    events = self._read_incoming_data(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 455, in _read_incoming_data
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_sync\http2.py", line 441, in _read_incoming_data
    data = self._network_stream.read(self.READ_NUM_BYTES, timeout)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_backends\sync.py", line 126, in read
    with map_exceptions(exc_map):
         ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError: [WinError 10054] An existing connection was forcibly closed by the remote host

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 599, in list_sites
    pages_dates = client.table('pages').select('snapshot_date').eq('site_id', site_id).execute().data or []
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 57, in execute
    r = self.session.request(
        self.http_method,
    ...<3 lines>...
        headers=self.headers,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 914, in send
    response = self._send_handling_auth(
        request,
    ...<2 lines>...
        history=[],
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
        request,
        follow_redirects=follow_redirects,
        history=history,
    )
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 249, in handle_request
    with map_httpcore_exceptions():
         ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-30 05:02:13,306 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:02:13,952 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:14,182 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:14,422 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,080 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,316 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,559 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:15,801 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:16,043 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:16,281 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:16,519 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:02:18,214 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 05:02:18,455 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1&snapshot_date=gte.2024-01-01&snapshot_date=lte.2024-12-31 "HTTP/2 200 OK"
2025-06-30 05:09:34,079 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:10:34,624 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:10:34,878 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,138 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,372 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,616 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:35,855 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,096 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,338 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:36,829 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:37,068 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:56,602 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:10:56,845 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,086 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,321 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,560 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:57,799 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,027 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,262 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,506 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,746 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:10:58,987 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:11:40,755 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:13:28,340 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:33:13,380 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:33:26,836 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:33:27,555 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:27,794 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:28,037 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:28,681 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:29,338 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:29,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:29,823 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:30,060 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:30,298 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:30,538 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:43,794 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:33:44,043 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:44,278 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:44,514 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:44,754 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,000 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,238 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,474 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,710 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:45,950 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:33:46,190 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:12,804 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:42:39,576 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:42:39,813 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,056 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,292 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,534 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:40,773 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,007 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,246 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,481 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:42:41,956 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:11,896 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:43:32,614 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:43:32,868 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,109 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,354 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,596 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:33,838 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,076 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,315 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,557 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:34,800 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:43:35,039 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:04,506 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:44:04,745 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:04,985 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,222 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,458 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,699 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:05,937 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,172 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,406 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,645 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:44:06,881 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:14,841 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:46:15,076 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:15,313 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:15,549 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:15,784 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,020 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,261 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,501 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,736 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:16,974 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:17,217 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:31,164 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:46:31,397 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:31,634 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:31,880 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,114 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,348 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,582 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:32,816 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:33,052 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:33,287 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:46:33,524 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:27,642 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:47:27,880 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,118 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,353 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:28,828 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,065 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,304 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,540 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:29,777 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:47:30,013 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:02,230 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:48:02,470 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:02,710 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:02,947 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,184 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,417 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,653 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:03,893 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:04,129 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:04,364 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:04,598 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:48:30,527 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:49:41,891 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 05:50:02,294 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:50:02,534 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:02,767 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,002 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,241 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,481 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,715 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:03,948 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:04,186 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:04,423 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:04,654 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:14,315 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:50:14,557 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:14,795 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,033 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,267 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,503 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,739 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:15,982 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:16,223 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:16,460 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:50:16,697 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:27,685 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:29,568 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 05:55:29,570 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 05:55:30,509 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:31,024 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:31,632 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:31,968 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:32,215 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:32,842 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\seo_report_boernevisioncenter_com_20250630_055529.xlsx
2025-06-30 05:55:36,686 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:55:36,923 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,157 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,391 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,626 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:37,866 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,104 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,343 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:38,818 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:55:39,052 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:54,457 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 05:58:54,691 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:54,924 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,157 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,392 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,626 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:55,857 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,097 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,332 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,569 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 05:58:56,802 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:27,111 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:28,934 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:00:28,936 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:00:29,184 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:29,671 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:30,245 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:30,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:30,817 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:31,443 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\seo_report_boernevisioncenter_com_20250630_060028.xlsx
2025-06-30 06:00:33,439 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:00:33,678 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:33,917 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,158 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,398 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,638 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:34,874 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,108 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,351 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,601 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:35,843 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:38,042 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:00:38,280 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:38,517 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:38,757 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,004 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,242 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,483 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:39,721 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:40,005 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:40,241 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:00:40,488 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:26,691 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:05:41,186 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:05:41,434 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:41,674 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:41,911 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,145 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,381 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,615 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:42,852 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:43,086 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:43,319 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:05:43,559 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:10,028 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:11,812 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:06:11,814 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:06:12,162 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:12,678 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:13,256 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:13,865 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:14,111 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:14,707 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_060611.xlsx
2025-06-30 06:06:18,693 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:06:18,929 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,170 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,407 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,640 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:19,875 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,109 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,342 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,575 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:20,810 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:21,047 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:31,624 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:33,398 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:06:33,399 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:06:33,652 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:34,152 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:34,769 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:35,174 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:35,412 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:06:35,839 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_060633.xlsx
2025-06-30 06:08:07,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:08:07,833 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,066 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,298 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,533 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:08,772 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,007 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,240 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,479 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:09,951 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:14,851 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:16,670 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:08:16,672 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:08:16,911 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:17,392 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:18,015 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:18,407 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:18,649 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:19,060 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_060816.xlsx
2025-06-30 06:08:22,893 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:08:23,133 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:23,367 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:23,621 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:23,853 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,090 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,325 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,557 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:24,790 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:25,025 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:08:25,260 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:21,510 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:09:21,747 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:21,987 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:36,764 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:09:37,006 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:37,247 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:37,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:37,966 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:38,433 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:38,678 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:38,917 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:39,173 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:39,411 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:09:39,652 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:14,994 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:11:34,553 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:36,256 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:11:36,259 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:11:36,597 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:37,115 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:37,669 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:38,251 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:38,499 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:39,001 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_061136.xlsx
2025-06-30 06:11:59,117 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:11:59,352 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:59,595 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:11:59,830 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,063 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,293 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,534 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:00,773 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:01,012 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:01,252 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:12:01,495 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:15,165 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:14:15,402 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:15,635 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:15,869 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,111 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,344 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,578 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:16,810 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:17,043 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:17,276 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:14:17,518 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:20:57,787 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:21:15,642 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:21:15,890 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:16,125 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:16,362 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:16,595 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:16,826 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:17,065 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:17,301 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:17,537 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:17,781 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:18,017 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:29,398 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:21:29,637 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:29,875 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:30,114 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:30,372 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:30,607 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:30,844 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:31,081 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:31,317 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:31,555 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:21:31,793 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:19,954 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:25:20,192 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:20,428 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:20,666 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:20,905 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:21,139 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:21,373 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:21,606 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:21,840 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:22,076 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:25:22,312 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:09,772 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:30:30,785 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:30:31,033 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:31,273 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:31,507 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:31,743 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:31,978 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:32,219 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:32,453 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:32,707 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:32,941 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:33,176 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:43,628 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:30:43,864 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:44,104 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:44,344 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:44,582 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:44,820 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:45,059 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:45,293 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:45,533 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:45,768 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:30:46,003 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:05,424 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:33:05,659 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:05,899 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:06,141 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:06,374 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:06,612 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:06,849 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:07,083 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:07,318 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:07,551 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:07,783 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:32,426 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:34,213 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 06:33:34,216 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 06:33:34,590 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:35,121 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:35,737 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:36,338 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:36,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:37,115 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_063334.xlsx
2025-06-30 06:33:40,915 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:33:41,155 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:41,395 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:41,632 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:41,870 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:42,107 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:42,344 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:42,576 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:42,815 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:43,054 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:33:43,288 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:38:56,623 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:39:23,593 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:39:23,834 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:24,073 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:24,306 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:24,540 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:24,775 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:25,008 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:25,239 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:25,474 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:25,719 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:39:25,954 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:15,777 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 06:40:40,734 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:40:40,975 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:41,214 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:41,452 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:41,688 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:41,929 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:42,165 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:42,401 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:42,637 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:42,876 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:43,114 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:52,094 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:40:52,332 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:52,570 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:52,840 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:53,075 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:53,311 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:53,565 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:53,806 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:54,046 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:54,287 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:54,525 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:56,359 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 06:40:56,599 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:56,844 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:57,084 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:57,321 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:57,558 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:57,793 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:58,030 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:58,271 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:58,509 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 06:40:58,745 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:12,711 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 07:39:31,165 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 07:39:31,823 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:32,060 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:32,731 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:32,972 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:33,210 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:33,863 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:34,102 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:34,339 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:34,580 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:34,817 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:45,368 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 07:39:45,609 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:45,851 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:46,101 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:46,344 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:46,590 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:46,832 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:47,072 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:47,311 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:47,553 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 07:39:47,796 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:11,935 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 09:53:12,223 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:12,480 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:13,172 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:13,433 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:14,103 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:14,355 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:15,028 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:15,277 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:15,529 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:15,787 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:24,697 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 09:53:24,954 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:25,219 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:25,483 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:25,749 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:25,988 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:26,234 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:26,501 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:26,748 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:26,991 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 09:53:27,265 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:29:58,611 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 12:29:58,879 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:29:59,601 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:29:59,861 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:00,535 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:00,806 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:01,089 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:01,341 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:01,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:01,849 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 12:30:02,526 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:39:23,575 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:39:25,810 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:39:26,505 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:39:26,529 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:39:28,864 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:39:30,743 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:39:30,996 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:39:30,997 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:39:32,718 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:39:34,485 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:39:34,732 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:39:34,733 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:39:41,581 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:39:43,384 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:39:43,624 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:39:43,625 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:43:54,010 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 13:44:12,305 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 13:44:35,920 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 13:45:03,453 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 13:45:47,110 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 13:46:06,333 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:08,022 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:46:08,023 - src.database.supabase_client - INFO - Attempting to update configuration for site boernevisioncenter.com (ID: 1)
2025-06-30 13:46:08,274 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:46:08,278 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:46:08,279 - src.database.supabase_client - ERROR - Full exception details:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 163, in update_site_configuration
    response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 78, in execute
    raise APIError(dict(json_obj))
postgrest.exceptions.APIError: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:46:08,309 - src.api.routes - ERROR - Failed to update configuration for site 1: Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:46:20,966 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:27,255 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 13:46:27,501 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:27,751 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:28,001 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:28,290 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:28,537 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:28,781 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:29,037 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:29,289 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:29,550 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:29,803 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:32,690 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 13:46:33,015 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:33,295 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:33,570 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:33,808 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:34,061 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:34,331 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:34,586 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:34,847 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:35,097 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:46:35,380 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:47:15,524 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:47:17,211 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:47:17,212 - src.database.supabase_client - INFO - Attempting to update configuration for site boernevisioncenter.com (ID: 1)
2025-06-30 13:47:17,475 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:47:17,476 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:47:17,476 - src.database.supabase_client - ERROR - Full exception details:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 163, in update_site_configuration
    response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 78, in execute
    raise APIError(dict(json_obj))
postgrest.exceptions.APIError: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:47:17,478 - src.api.routes - ERROR - Failed to update configuration for site 1: Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:47:51,539 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:47:53,012 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:47:53,013 - src.database.supabase_client - INFO - Attempting to update configuration for site boernevisioncenter.com (ID: 1)
2025-06-30 13:47:53,261 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:47:53,262 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:47:53,262 - src.database.supabase_client - ERROR - Full exception details:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 163, in update_site_configuration
    response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 78, in execute
    raise APIError(dict(json_obj))
postgrest.exceptions.APIError: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:47:53,264 - src.api.routes - ERROR - Failed to update configuration for site 1: Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:47:59,157 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 13:47:59,404 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:47:59,648 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:47:59,902 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:00,147 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:00,400 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:00,648 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:00,919 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:01,170 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:01,408 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:01,654 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:23,326 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 13:48:25,110 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 13:48:25,111 - src.database.supabase_client - INFO - Attempting to update configuration for site boernevisioncenter.com (ID: 1)
2025-06-30 13:48:25,384 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 400 Bad Request"
2025-06-30 13:48:25,386 - src.database.supabase_client - ERROR - Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:48:25,386 - src.database.supabase_client - ERROR - Full exception details:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 163, in update_site_configuration
    response = self.client.table('sites').update(config_data).eq('id', self.site_id).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 78, in execute
    raise APIError(dict(json_obj))
postgrest.exceptions.APIError: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 13:48:25,388 - src.api.routes - ERROR - Failed to update configuration for site 1: Error updating site configuration: {'message': "Could not find the 'domain_property' column of 'sites' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:04:20,601 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:04:44,635 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:05:12,230 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:05:12,485 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:13,010 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:13,318 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:13,650 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:13,997 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:14,309 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:14,606 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:14,898 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:15,194 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:15,494 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:44,875 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:46,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:05:46,592 - src.database.supabase_client - INFO - Attempting to update configuration for site boernevisioncenter.com (ID: 1)
2025-06-30 14:05:46,871 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:46,874 - src.database.supabase_client - INFO - Successfully updated configuration for site boernevisioncenter.com
2025-06-30 14:05:50,684 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:05:50,937 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:51,202 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:51,459 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:51,722 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:51,972 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:52,237 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:52,496 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:52,746 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:53,004 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:05:53,254 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:07,546 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:06:07,810 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:08,091 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:08,348 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:08,645 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:08,906 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:09,162 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:09,427 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:09,684 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:09,953 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:06:10,213 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:26,580 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:08:48,673 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:08:48,924 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:49,179 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:49,439 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:49,688 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:49,933 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:50,184 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:50,445 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:50,698 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:50,949 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:08:51,203 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:03,505 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:09:03,753 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:04,010 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:04,277 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:04,519 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:04,770 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:05,027 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:05,288 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:05,543 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:05,826 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:06,092 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:09:25,680 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq. "HTTP/2 200 OK"
2025-06-30 14:09:25,981 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites "HTTP/2 400 Bad Request"
2025-06-30 14:09:25,985 - src.api.routes - ERROR - Error processing re-analysis request:
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 601, in reanalyze_site
    supabase_client = SupabaseClient(
        url=settings.supabase_url,
        key=settings.supabase_key,
        domain=""  # Will be set when we get the site info
    )
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 31, in __init__
    self.site_id = self._get_or_create_site_id()
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 51, in _get_or_create_site_id
    response = self.client.table('sites').insert(site_data).execute()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\postgrest\_sync\request_builder.py", line 78, in execute
    raise APIError(dict(json_obj))
postgrest.exceptions.APIError: {'message': 'cannot insert a non-DEFAULT value into column "id"', 'code': '428C9', 'hint': 'Use OVERRIDING SYSTEM VALUE to override.', 'details': 'Column "id" is an identity column defined as GENERATED ALWAYS.'}
2025-06-30 14:12:41,331 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:15:22,642 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:15:22,939 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:23,202 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:23,458 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:23,727 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:23,999 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:24,265 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:24,537 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:24,809 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:25,071 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:25,327 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:35,907 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:15:37,024 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:15:37,127 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:15:50,081 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id%2Cdomain&domain=eq.test-site.com "HTTP/2 200 OK"
2025-06-30 14:15:50,344 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites "HTTP/2 201 Created"
2025-06-30 14:15:50,346 - src.database.supabase_client - INFO - Created site test-site.com with configuration (ID: 2)
2025-06-30 14:15:54,886 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:15:56,494 - src.core.crawler - WARNING - Skipping dead link: https://stg-boerne-boernestag.kinsta.cloud (Status code: 503)
2025-06-30 14:15:56,495 - src.services.analysis_service - ERROR - Error in SEO analysis for task 960d32eb-3b52-4731-8e3b-ec027c766685
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 112, in run_analysis
    crawl_results = self.crawler.crawl_site(website_urls, output_dir)
  File "C:\Gautam\Projects\Scraper\src\core\crawler.py", line 114, in crawl_site
    data = asyncio.run(self.crawl_url_with_js(url))
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\runners.py", line 191, in run
    raise RuntimeError(
        "asyncio.run() cannot be called from a running event loop")
RuntimeError: asyncio.run() cannot be called from a running event loop
2025-06-30 14:15:56,497 - src.api.routes - ERROR - Error in background analysis task 960d32eb-3b52-4731-8e3b-ec027c766685
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 57, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 112, in run_analysis
    crawl_results = self.crawler.crawl_site(website_urls, output_dir)
  File "C:\Gautam\Projects\Scraper\src\core\crawler.py", line 114, in crawl_site
    data = asyncio.run(self.crawl_url_with_js(url))
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\runners.py", line 191, in run
    raise RuntimeError(
        "asyncio.run() cannot be called from a running event loop")
RuntimeError: asyncio.run() cannot be called from a running event loop
2025-06-30 14:16:02,214 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:04,062 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.test-site.com "HTTP/2 200 OK"
2025-06-30 14:16:04,321 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:04,578 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:04,836 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:05,093 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:05,338 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:05,594 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:05,595 - src.database.supabase_client - INFO - Cleared configuration for site test-site.com
2025-06-30 14:16:05,595 - src.database.supabase_client - INFO - Site data deletion summary for test-site.com: {'pages': 0, 'gsc_keywords': 0, 'gsc_traffic': 0, 'internal_links': 0, 'ga_data': 0}
2025-06-30 14:16:05,851 - httpx - INFO - HTTP Request: DELETE https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.2 "HTTP/2 200 OK"
2025-06-30 14:16:05,852 - src.database.supabase_client - INFO - Completely deleted site test-site.com
2025-06-30 14:16:17,421 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:16:17,689 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:17,957 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:18,227 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:18,494 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:18,758 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:19,013 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:19,272 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:19,539 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:19,826 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:16:20,078 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:18:36,210 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:18:38,006 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:18:38,027 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:18:39,043 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:18:40,230 - src.core.crawler - WARNING - Skipping dead link: https://stg-boerne-boernestag.kinsta.cloud (Status code: 503)
2025-06-30 14:18:40,231 - src.services.analysis_service - ERROR - Error in SEO analysis for task 5a3b3199-3179-4b7c-bd12-701b4716262d
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 112, in run_analysis
    crawl_results = self.crawler.crawl_site(website_urls, output_dir)
  File "C:\Gautam\Projects\Scraper\src\core\crawler.py", line 114, in crawl_site
    data = asyncio.run(self.crawl_url_with_js(url))
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\runners.py", line 191, in run
    raise RuntimeError(
        "asyncio.run() cannot be called from a running event loop")
RuntimeError: asyncio.run() cannot be called from a running event loop
2025-06-30 14:18:40,233 - src.api.routes - ERROR - Error in background analysis task 5a3b3199-3179-4b7c-bd12-701b4716262d
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 57, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 112, in run_analysis
    crawl_results = self.crawler.crawl_site(website_urls, output_dir)
  File "C:\Gautam\Projects\Scraper\src\core\crawler.py", line 114, in crawl_site
    data = asyncio.run(self.crawl_url_with_js(url))
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\runners.py", line 191, in run
    raise RuntimeError(
        "asyncio.run() cannot be called from a running event loop")
RuntimeError: asyncio.run() cannot be called from a running event loop
2025-06-30 14:22:34,816 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:23:03,374 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:23:04,562 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:23:04,583 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:23:11,386 - src.core.crawler - INFO - Discovered 1 URLs
2025-06-30 14:23:11,387 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:23:12,268 - src.core.crawler - WARNING - Skipping dead link: https://stg-boerne-boernestag.kinsta.cloud (Status code: 503)
2025-06-30 14:23:12,268 - src.core.crawler - INFO - Trying JS rendering for https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:23:14,421 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:23:20,218 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:23:22,147 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:23:24,150 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:23:25,901 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:23:27,940 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:23:29,310 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:23:30,892 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:23:32,449 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:23:33,738 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:23:34,961 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:23:36,400 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:23:38,543 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:23:39,140 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:23:39,435 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:23:39,836 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:23:40,293 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:23:40,717 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:23:41,122 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:23:41,529 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:23:41,919 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:23:42,394 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:23:42,846 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:23:43,312 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:23:43,723 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:23:44,117 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:23:44,538 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:23:45,025 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:23:45,030 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 14:23:45,037 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 14:23:45,040 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 14:23:45,042 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 14:23:45,044 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 14:23:45,046 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 14:23:45,047 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 14:23:45,049 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 14:23:45,051 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 14:23:45,053 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 14:23:45,056 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 14:23:45,058 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 14:23:45,060 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 14:23:45,061 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 14:23:45,099 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 14:23:45,100 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 14:23:46,358 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:23:46,365 - src.services.analysis_service - ERROR - Error in SEO analysis for task 172fdb8f-2c02-4c2b-8023-38055998e3c4
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'URL'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 167, in run_analysis
    supabase_client.save_pages_data(data_df)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 265, in save_pages_data
    df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())
                     ~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'URL'
2025-06-30 14:23:46,464 - src.api.routes - ERROR - Error in background analysis task 172fdb8f-2c02-4c2b-8023-38055998e3c4
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'URL'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 57, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 167, in run_analysis
    supabase_client.save_pages_data(data_df)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 265, in save_pages_data
    df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())
                     ~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'URL'
2025-06-30 14:34:33,612 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:34:35,461 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:34:35,532 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:34:42,283 - src.core.crawler - INFO - Discovered 1 URLs
2025-06-30 14:34:42,285 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:34:43,182 - src.core.crawler - WARNING - Skipping dead link: https://stg-boerne-boernestag.kinsta.cloud (Status code: 503)
2025-06-30 14:34:43,183 - src.core.crawler - INFO - Trying JS rendering for https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:34:44,820 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:34:51,386 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:34:53,339 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:34:55,086 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:34:56,740 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:34:58,769 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:35:00,914 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:35:02,298 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:35:03,843 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:35:05,188 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:35:06,549 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:35:07,947 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:35:09,835 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:35:10,418 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:35:10,770 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:35:11,248 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:35:11,644 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:35:12,071 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:35:12,531 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:35:12,930 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:35:13,320 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:35:13,817 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:35:14,318 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:35:14,827 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:35:15,237 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:35:15,688 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:35:16,110 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:35:16,498 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:35:16,502 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 14:35:16,513 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 14:35:16,516 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 14:35:16,519 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 14:35:16,521 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 14:35:16,524 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 14:35:16,526 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 14:35:16,529 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 14:35:16,533 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 14:35:16,535 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 14:35:16,538 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 14:35:16,541 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 14:35:16,544 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 14:35:16,546 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 14:35:16,561 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 14:35:16,562 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 14:35:18,716 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:35:18,722 - src.services.analysis_service - ERROR - Error in SEO analysis for task e1a1acf4-7346-41d0-8c0c-c25deccffd44
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'URL'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 167, in run_analysis
    supabase_client.save_pages_data(data_df)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 265, in save_pages_data
    df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())
                     ~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'URL'
2025-06-30 14:35:18,732 - src.api.routes - ERROR - Error in background analysis task e1a1acf4-7346-41d0-8c0c-c25deccffd44
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'URL'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\src\api\routes.py", line 57, in run_seo_analysis
    await analysis_service.run_analysis(
    ...<2 lines>...
    )
  File "C:\Gautam\Projects\Scraper\src\services\analysis_service.py", line 167, in run_analysis
    supabase_client.save_pages_data(data_df)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Gautam\Projects\Scraper\src\database\supabase_client.py", line 265, in save_pages_data
    df['url_hash'] = df['URL'].apply(lambda x: hashlib.md5(x.encode()).hexdigest())
                     ~~^^^^^^^
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'URL'
2025-06-30 14:39:16,601 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:39:30,733 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:39:50,578 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:40:11,363 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:40:11,615 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:11,857 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:12,135 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:12,813 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:13,066 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:13,310 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:13,562 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:13,805 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:14,071 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:40:14,314 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:43:06,349 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:43:08,217 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:43:08,392 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:43:08,418 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-8' coro=<Connection.run() done, defined at C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        stderr=stderr, **kwds)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        protocol, popen_args, False, stdin, stdout, stderr,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        bufsize, **kwargs)
        ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-06-30 14:43:08,572 - src.core.crawler - ERROR - Failed to discover URLs from homepage https://stg-boerne-boernestag.kinsta.cloud: 
2025-06-30 14:43:08,573 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:43:18,736 - src.core.crawler - ERROR - Error crawling https://stg-boerne-boernestag.kinsta.cloud: HTTPSConnectionPool(host='stg-boerne-boernestag.kinsta.cloud', port=443): Read timed out. (read timeout=10)
2025-06-30 14:43:18,736 - src.core.crawler - INFO - Trying JS rendering for https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:43:18,737 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-11' coro=<Connection.run() done, defined at C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        stderr=stderr, **kwds)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        protocol, popen_args, False, stdin, stdout, stderr,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        bufsize, **kwargs)
        ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-06-30 14:43:18,742 - src.core.crawler - ERROR - JS rendering failed for https://stg-boerne-boernestag.kinsta.cloud: 
2025-06-30 14:43:18,743 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:43:24,956 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:43:27,195 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:43:28,998 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:43:31,144 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:43:32,767 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:43:34,386 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:43:35,991 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:43:37,282 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:43:38,651 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:43:39,961 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:43:41,282 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:43:43,124 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:43:43,740 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:43:44,152 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:43:44,588 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:43:44,990 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:43:45,395 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:43:45,797 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:43:46,314 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:43:46,758 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:43:47,151 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:43:47,597 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:43:48,101 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:43:48,495 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:43:48,947 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:43:49,357 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:43:49,823 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:43:49,827 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 14:43:49,834 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 14:43:49,838 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 14:43:49,840 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 14:43:49,842 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 14:43:49,845 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 14:43:49,848 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 14:43:49,851 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 14:43:49,854 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 14:43:49,856 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 14:43:49,859 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 14:43:49,862 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 14:43:49,865 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 14:43:49,867 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 14:43:49,889 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 14:43:49,889 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 14:43:51,690 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:44:30,533 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2Ckeyword_hash&columns=%22keyword_hash%22%2C%22ctr%22%2C%22page%22%2C%22query%22%2C%22clicks%22%2C%22position%22%2C%22impressions%22%2C%22Month%22%2C%22site_id%22 "HTTP/2 400 Bad Request"
2025-06-30 14:44:30,547 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': "Could not find the 'clicks' column of 'gsc_keywords' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:44:31,597 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2Ctraffic_hash&columns=%22ctr%22%2C%22page%22%2C%22traffic_hash%22%2C%22clicks%22%2C%22position%22%2C%22impressions%22%2C%22Month%22%2C%22site_id%22 "HTTP/2 400 Bad Request"
2025-06-30 14:44:31,598 - src.database.supabase_client - ERROR - Error saving GSC traffic: {'message': "Could not find the 'clicks' column of 'gsc_traffic' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:44:35,229 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:44:35,502 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:35,762 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:36,019 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:36,271 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:36,528 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:36,783 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:37,038 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:37,304 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:37,575 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:44:37,839 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:46:04,381 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:46:27,666 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:46:43,541 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:46:59,730 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:47:22,911 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:47:33,633 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:47:49,401 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:48:02,068 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:48:20,965 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:48:41,680 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:48:51,994 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:49:08,154 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:51:17,198 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:51:19,230 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:51:19,259 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:51:19,260 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-5' coro=<Connection.run() done, defined at C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        stderr=stderr, **kwds)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        protocol, popen_args, False, stdin, stdout, stderr,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        bufsize, **kwargs)
        ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-06-30 14:51:19,271 - src.core.crawler - ERROR - Failed to discover URLs from homepage https://stg-boerne-boernestag.kinsta.cloud: 
2025-06-30 14:51:19,272 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:51:25,510 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:51:31,725 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:51:33,637 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:51:35,652 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:51:37,722 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:51:39,327 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:51:40,877 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:51:42,487 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:51:44,032 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:51:45,452 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:51:46,702 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:51:48,092 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:51:49,806 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:51:50,391 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:51:50,799 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:51:51,241 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:51:51,642 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:51:52,099 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:51:52,491 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:51:53,009 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:51:53,434 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:51:53,892 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:51:54,283 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:51:54,685 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:51:55,118 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:51:55,559 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:51:55,984 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:51:56,369 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:51:56,377 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 14:51:56,388 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 14:51:56,391 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 14:51:56,394 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 14:51:56,397 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 14:51:56,401 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 14:51:56,406 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 14:51:56,409 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 14:51:56,412 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 14:51:56,415 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 14:51:56,417 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 14:51:56,421 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 14:51:56,426 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 14:51:56,428 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 14:51:56,441 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 14:51:56,442 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 14:51:59,240 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:52:01,415 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?on_conflict=site_id%2Curl_hash%2Csnapshot_date&columns=%22url_hash%22%2C%22raw_html%22%2C%22SEO+Title%22%2C%22Page+Content%22%2C%22snapshot_date%22%2C%22Meta+Description%22%2C%22H1%22%2C%22site_id%22%2C%22URL%22 "HTTP/2 400 Bad Request"
2025-06-30 14:52:01,418 - src.database.supabase_client - ERROR - Error saving pages data: {'message': "Could not find the 'raw_html' column of 'pages' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:52:36,952 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2Ckeyword_hash&columns=%22Impressions%22%2C%22CTR%22%2C%22Keyword%22%2C%22Clicks%22%2C%22Position%22%2C%22Month%22%2C%22keyword_hash%22%2C%22site_id%22%2C%22URL%22 "HTTP/2 400 Bad Request"
2025-06-30 14:52:36,954 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': "Could not find the 'keyword_hash' column of 'gsc_keywords' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:52:37,381 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2Ctraffic_hash&columns=%22Impressions%22%2C%22CTR%22%2C%22Position%22%2C%22Clicks%22%2C%22Month%22%2C%22traffic_hash%22%2C%22site_id%22%2C%22URL%22 "HTTP/2 400 Bad Request"
2025-06-30 14:52:37,382 - src.database.supabase_client - ERROR - Error saving GSC traffic: {'message': "Could not find the 'traffic_hash' column of 'gsc_traffic' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:52:43,102 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:52:43,363 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:43,611 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:43,870 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:44,119 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:44,366 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:44,621 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:44,878 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:45,124 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:45,367 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:52:45,618 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:54:40,957 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 14:55:25,347 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:55:25,603 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:25,857 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:26,110 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:26,349 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:26,591 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:26,845 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:27,079 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:27,331 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:27,581 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:27,822 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:33,194 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:55:34,903 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:55:34,951 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 14:56:09,354 - src.core.crawler - INFO - Discovered 45 URLs
2025-06-30 14:56:09,355 - src.utils.file_utils - INFO - Saved 45 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 14:58:38,189 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:58:43,653 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:58:45,779 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:58:47,846 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:58:49,605 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:58:51,271 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:58:52,675 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:58:54,091 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:58:55,549 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:58:56,850 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:58:58,239 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:58:59,800 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:59:01,698 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:59:02,264 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:59:02,671 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 14:59:03,119 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 14:59:03,525 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 14:59:03,932 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 14:59:04,384 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 14:59:04,783 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 14:59:05,252 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 14:59:05,667 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 14:59:06,127 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 14:59:06,527 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 14:59:06,966 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 14:59:07,385 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 14:59:07,800 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 14:59:08,189 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 14:59:08,194 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 14:59:08,206 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 14:59:08,209 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 14:59:08,211 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 14:59:08,214 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 14:59:08,216 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 14:59:08,219 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 14:59:08,221 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 14:59:08,223 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 14:59:08,226 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 14:59:08,230 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 14:59:08,231 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 14:59:08,234 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 14:59:08,237 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 14:59:08,248 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 14:59:08,249 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 14:59:14,134 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 14:59:16,669 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?on_conflict=site_id%2Curl_hash%2Csnapshot_date&columns=%22raw_html%22%2C%22snapshot_date%22%2C%22Meta+Description%22%2C%22URL%22%2C%22SEO+Title%22%2C%22site_id%22%2C%22H1%22%2C%22Page+Content%22%2C%22url_hash%22 "HTTP/2 400 Bad Request"
2025-06-30 14:59:16,672 - src.database.supabase_client - ERROR - Error saving pages data: {'message': "Could not find the 'raw_html' column of 'pages' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:59:51,892 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2Ckeyword_hash&columns=%22Keyword%22%2C%22Impressions%22%2C%22Position%22%2C%22Month%22%2C%22Clicks%22%2C%22URL%22%2C%22keyword_hash%22%2C%22site_id%22%2C%22CTR%22 "HTTP/2 400 Bad Request"
2025-06-30 14:59:51,893 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': "Could not find the 'keyword_hash' column of 'gsc_keywords' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:59:52,416 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2Ctraffic_hash&columns=%22Impressions%22%2C%22Position%22%2C%22Month%22%2C%22traffic_hash%22%2C%22Clicks%22%2C%22URL%22%2C%22site_id%22%2C%22CTR%22 "HTTP/2 400 Bad Request"
2025-06-30 14:59:52,417 - src.database.supabase_client - ERROR - Error saving GSC traffic: {'message': "Could not find the 'traffic_hash' column of 'gsc_traffic' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-30 14:59:56,875 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 14:59:57,122 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:57,364 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:57,606 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:57,857 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:58,113 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:58,361 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:58,604 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:58,849 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:59,104 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 14:59:59,346 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:21:34,775 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:21:36,984 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 15:21:37,137 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 15:22:11,174 - src.core.crawler - INFO - Discovered 45 URLs
2025-06-30 15:22:11,177 - src.utils.file_utils - INFO - Saved 45 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 15:24:30,485 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 15:24:36,367 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 15:24:38,524 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 15:24:40,565 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 15:24:42,333 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 15:24:44,179 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 15:24:45,708 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 15:24:47,375 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 15:24:48,990 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 15:24:50,278 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 15:24:51,523 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 15:24:52,852 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 15:24:54,820 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 15:24:55,395 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 15:24:55,861 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 15:24:56,286 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 15:24:56,718 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 15:24:57,107 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 15:24:57,561 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 15:24:58,017 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 15:24:58,404 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 15:24:58,858 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 15:24:59,254 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 15:24:59,707 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 15:25:00,168 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 15:25:00,629 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 15:25:01,041 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 15:25:01,431 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 15:25:01,436 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 15:25:01,446 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 15:25:01,450 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 15:25:01,452 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 15:25:01,454 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 15:25:01,458 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 15:25:01,460 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 15:25:01,462 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 15:25:01,465 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 15:25:01,468 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 15:25:01,471 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 15:25:01,474 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 15:25:01,477 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 15:25:01,481 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 15:25:01,495 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 15:25:01,496 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 15:25:07,145 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 15:25:11,605 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?on_conflict=site_id%2Curl_hash%2Csnapshot_date&columns=%22raw_html%22%2C%22snapshot_date%22%2C%22Meta+Description%22%2C%22URL%22%2C%22SEO+Title%22%2C%22site_id%22%2C%22H1%22%2C%22Page+Content%22%2C%22url_hash%22 "HTTP/2 400 Bad Request"
2025-06-30 15:25:11,608 - src.database.supabase_client - ERROR - Error saving pages data: {'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification', 'code': '42P10', 'hint': None, 'details': None}
2025-06-30 15:25:45,005 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2Ckeyword_hash&columns=%22Keyword%22%2C%22Impressions%22%2C%22Position%22%2C%22Month%22%2C%22Clicks%22%2C%22URL%22%2C%22keyword_hash%22%2C%22site_id%22%2C%22CTR%22 "HTTP/2 400 Bad Request"
2025-06-30 15:25:45,006 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification', 'code': '42P10', 'hint': None, 'details': None}
2025-06-30 15:25:46,453 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2Ctraffic_hash&columns=%22Impressions%22%2C%22Position%22%2C%22Month%22%2C%22traffic_hash%22%2C%22Clicks%22%2C%22URL%22%2C%22site_id%22%2C%22CTR%22 "HTTP/2 400 Bad Request"
2025-06-30 15:25:46,454 - src.database.supabase_client - ERROR - Error saving GSC traffic: {'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification', 'code': '42P10', 'hint': None, 'details': None}
2025-06-30 15:25:50,831 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 15:25:51,108 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:51,360 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:51,657 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:51,923 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:52,173 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:52,429 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:52,673 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:52,929 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:53,196 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:25:53,435 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:36:35,488 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:49:17,362 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:51:33,950 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:53:01,676 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:53:30,365 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 15:53:31,069 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:31,733 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:31,982 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:32,229 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:32,484 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:32,727 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:32,967 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:33,208 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:33,457 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:33,704 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:39,007 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 15:53:39,257 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:39,502 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:39,754 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:39,994 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:40,239 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:40,479 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:40,732 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:41,000 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:41,246 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:41,487 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:43,788 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 15:53:44,036 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:44,302 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:44,579 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:44,841 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:45,079 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:45,337 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:45,600 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:45,850 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:46,092 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:53:46,345 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:06,972 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:08,870 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 15:54:08,873 - src.database.supabase_client - INFO - Attempting to update configuration for site boernevisioncenter.com (ID: 1)
2025-06-30 15:54:09,123 - httpx - INFO - HTTP Request: PATCH https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:09,125 - src.database.supabase_client - INFO - Successfully updated configuration for site boernevisioncenter.com
2025-06-30 15:54:13,039 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 15:54:13,309 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:13,563 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:13,847 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:14,101 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:14,346 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:14,599 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:14,857 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:15,108 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:15,376 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:15,619 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:28,624 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:54:30,598 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 15:54:30,627 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 15:54:30,629 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-18' coro=<Connection.run() done, defined at C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py:303> exception=NotImplementedError()>
Traceback (most recent call last):
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_connection.py", line 310, in run
    await self._transport.connect()
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 133, in connect
    raise exc
  File "C:\Gautam\Projects\Scraper\deactivate\Lib\site-packages\playwright\_impl\_transport.py", line 120, in connect
    self._proc = await asyncio.create_subprocess_exec(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\subprocess.py", line 224, in create_subprocess_exec
    transport, protocol = await loop.subprocess_exec(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        stderr=stderr, **kwds)
        ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1794, in subprocess_exec
    transport = await self._make_subprocess_transport(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        protocol, popen_args, False, stdin, stdout, stderr,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        bufsize, **kwargs)
        ^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1520.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 539, in _make_subprocess_transport
    raise NotImplementedError
NotImplementedError
2025-06-30 15:54:30,640 - src.core.crawler - ERROR - Failed to discover URLs from homepage https://stg-boerne-boernestag.kinsta.cloud: 
2025-06-30 15:54:30,641 - src.utils.file_utils - INFO - Saved 1 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 15:54:34,308 - src.core.wordpress - INFO - Found WordPress API endpoint: https://boernevisioncenter.com/wp-json/wp/v2/
2025-06-30 15:54:34,309 - src.core.wordpress - INFO - Fetching data from WordPress API: https://boernevisioncenter.com/wp-json/wp/v2/
2025-06-30 15:54:35,099 - src.core.wordpress - INFO - Successfully fetched data from WordPress API.
2025-06-30 15:54:41,009 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 15:54:47,361 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 15:54:49,245 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 15:54:51,110 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 15:54:53,247 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 15:54:54,803 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 15:54:57,883 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 15:54:59,302 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 15:55:00,770 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 15:55:02,036 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 15:55:03,185 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 15:55:04,758 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 15:55:06,554 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 15:55:07,065 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 15:55:07,312 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 15:55:07,777 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 15:55:08,266 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 15:55:08,658 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 15:55:09,109 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 15:55:09,511 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 15:55:09,900 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 15:55:10,295 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 15:55:10,769 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 15:55:11,224 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 15:55:11,855 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 15:55:12,256 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 15:55:12,751 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 15:55:13,150 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 15:55:13,161 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 15:55:13,165 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: Missing required parameter "property"
2025-06-30 15:55:13,167 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: Missing required parameter "property"
2025-06-30 15:55:13,169 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: Missing required parameter "property"
2025-06-30 15:55:13,170 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: Missing required parameter "property"
2025-06-30 15:55:13,172 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: Missing required parameter "property"
2025-06-30 15:55:13,173 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: Missing required parameter "property"
2025-06-30 15:55:13,174 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: Missing required parameter "property"
2025-06-30 15:55:13,176 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: Missing required parameter "property"
2025-06-30 15:55:13,178 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: Missing required parameter "property"
2025-06-30 15:55:13,179 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: Missing required parameter "property"
2025-06-30 15:55:13,182 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: Missing required parameter "property"
2025-06-30 15:55:13,184 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: Missing required parameter "property"
2025-06-30 15:55:13,186 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: Missing required parameter "property"
2025-06-30 15:55:13,193 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 15:55:13,194 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 15:55:14,722 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 15:55:15,852 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?on_conflict=site_id%2Curl_hash%2Csnapshot_date&columns=%22Meta+Description%22%2C%22H1%22%2C%22snapshot_date%22%2C%22SEO+Title%22%2C%22URL%22%2C%22url_hash%22%2C%22site_id%22%2C%22Page+Content%22%2C%22raw_html%22 "HTTP/2 400 Bad Request"
2025-06-30 15:55:15,855 - src.database.supabase_client - ERROR - Error saving pages data: {'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification', 'code': '42P10', 'hint': None, 'details': None}
2025-06-30 15:55:50,062 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2Ckeyword_hash&columns=%22site_id%22%2C%22Position%22%2C%22Keyword%22%2C%22Clicks%22%2C%22CTR%22%2C%22URL%22%2C%22Month%22%2C%22Impressions%22%2C%22keyword_hash%22 "HTTP/2 400 Bad Request"
2025-06-30 15:55:50,063 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification', 'code': '42P10', 'hint': None, 'details': None}
2025-06-30 15:55:51,999 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2Ctraffic_hash&columns=%22site_id%22%2C%22Position%22%2C%22Clicks%22%2C%22traffic_hash%22%2C%22CTR%22%2C%22URL%22%2C%22Month%22%2C%22Impressions%22 "HTTP/2 400 Bad Request"
2025-06-30 15:55:52,000 - src.database.supabase_client - ERROR - Error saving GSC traffic: {'message': 'there is no unique or exclusion constraint matching the ON CONFLICT specification', 'code': '42P10', 'hint': None, 'details': None}
2025-06-30 15:55:55,510 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 15:55:55,792 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:56,079 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:56,363 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:56,634 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:56,889 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:57,140 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:57,397 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:57,656 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:57,928 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:55:58,224 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 15:56:25,619 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:56:25,813 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:56:42,386 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 15:56:42,715 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:00:53,183 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:00:53,184 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:01:24,819 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:01:28,239 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:01:57,323 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:01:58,038 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:02:15,918 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:02:16,173 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:16,442 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:16,700 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:16,947 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:17,194 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:17,446 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:17,704 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:17,980 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:18,235 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:18,497 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:43,711 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:02:44,738 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:45,004 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:45,265 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:45,517 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:45,772 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:46,016 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:46,268 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:46,518 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:46,776 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:02:47,036 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:12:21,072 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:26:20,118 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:26:20,803 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:21,051 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:21,285 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:21,535 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:22,224 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:22,455 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:22,720 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:22,971 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:23,208 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:26:23,456 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:31,630 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:28:31,900 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:32,146 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:32,418 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:33,101 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:33,390 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:33,652 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:33,906 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:34,166 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:34,415 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:28:34,666 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:16,240 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:29:16,488 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:16,737 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:16,989 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:17,226 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:17,494 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:17,745 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:18,019 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:18,261 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:18,523 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:29:18,781 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:02,205 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:30:02,468 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:02,732 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:03,005 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:03,279 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:03,531 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:03,798 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:04,056 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:04,301 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:04,579 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:04,856 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:28,343 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:30:30,097 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 16:30:30,259 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 16:31:17,148 - src.core.crawler - INFO - Discovered 45 URLs
2025-06-30 16:31:17,149 - src.utils.file_utils - INFO - Saved 45 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 16:31:21,178 - src.core.wordpress - INFO - Found WordPress API endpoint: https://boernevisioncenter.com/wp-json/wp/v2/
2025-06-30 16:31:21,178 - src.core.wordpress - INFO - Fetching data from WordPress API: https://boernevisioncenter.com/wp-json/wp/v2/
2025-06-30 16:31:21,705 - src.core.wordpress - INFO - Successfully fetched data from WordPress API.
2025-06-30 16:33:41,912 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 16:36:04,920 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 16:36:07,141 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 16:36:08,895 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 16:36:10,894 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 16:36:12,721 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 16:36:14,114 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 16:36:15,633 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 16:36:17,256 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 16:36:18,503 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 16:36:19,887 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 16:36:21,449 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 16:36:23,294 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 16:36:23,898 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 16:36:24,363 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 16:36:24,777 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 16:36:25,296 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 16:36:25,706 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 16:36:26,295 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 16:36:26,783 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 16:36:27,287 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 16:36:27,752 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 16:36:28,160 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 16:36:28,691 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 16:36:29,168 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 16:36:29,674 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 16:36:30,098 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 16:36:30,576 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 16:36:30,584 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 16:36:34,360 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:34,690 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:35,896 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:36,221 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:37,391 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:38,552 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:38,886 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:39,219 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:40,389 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:40,716 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:41,886 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:43,051 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:43,377 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:36:43,409 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 16:36:43,410 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 16:36:47,347 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 16:36:53,875 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?on_conflict=site_id%2CURL%2Csnapshot_date&columns=%22site_id%22%2C%22Page+Content%22%2C%22URL%22%2C%22Meta+Description%22%2C%22raw_html%22%2C%22H1%22%2C%22url_hash%22%2C%22snapshot_date%22%2C%22SEO+Title%22 "HTTP/2 201 Created"
2025-06-30 16:36:55,184 - src.database.supabase_client - INFO - Saved 45 pages to Supabase
2025-06-30 16:37:48,458 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2CURL%2CKeyword%2CMonth&columns=%22site_id%22%2C%22Month%22%2C%22URL%22%2C%22Position%22%2C%22Clicks%22%2C%22Keyword%22%2C%22keyword_hash%22%2C%22Impressions%22%2C%22CTR%22 "HTTP/2 500 Internal Server Error"
2025-06-30 16:37:48,461 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': 'canceling statement due to statement timeout', 'code': '57014', 'hint': None, 'details': None}
2025-06-30 16:37:50,722 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2CURL%2CMonth&columns=%22site_id%22%2C%22Month%22%2C%22traffic_hash%22%2C%22URL%22%2C%22Position%22%2C%22Clicks%22%2C%22Impressions%22%2C%22CTR%22 "HTTP/2 201 Created"
2025-06-30 16:37:51,187 - src.database.supabase_client - INFO - Saved 1516 GSC traffic records to Supabase
2025-06-30 16:44:09,162 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:44:09,449 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:09,739 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:10,011 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:10,276 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:10,554 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:10,807 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:11,081 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:11,335 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:11,604 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:44:11,867 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:15,914 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:46:20,707 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:46:20,947 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:21,212 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:21,489 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:21,739 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:21,998 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:22,262 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:22,980 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:23,250 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:23,501 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:23,752 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:48,772 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 16:46:56,003 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:46:56,254 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:56,531 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:56,943 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:57,204 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:57,465 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:57,733 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:57,988 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:58,231 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:58,477 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:46:58,745 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:43,050 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:48:43,306 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:43,563 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:43,811 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:44,065 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:44,316 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:44,558 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:44,818 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:45,076 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:45,330 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:48:45,622 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:35,728 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:49:36,048 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:36,313 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:36,568 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:37,456 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:37,717 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:37,992 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:38,248 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:38,498 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:38,751 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:49:39,012 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:07,501 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:50:07,761 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:08,025 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:08,283 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:08,555 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:08,817 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:09,078 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:09,339 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:09,621 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:09,883 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:10,143 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:28,813 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:50:29,067 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:29,336 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:29,604 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:29,867 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:30,121 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:30,397 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:30,657 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:30,927 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:31,178 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:50:31,426 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:51,210 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=domain&id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:53,033 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 16:51:53,041 - src.api.routes - INFO - Generating enhanced Excel report for domain: boernevisioncenter.com
2025-06-30 16:51:54,827 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:56,982 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:57,711 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:58,426 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:58,704 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=%2A&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:51:59,500 - src.api.routes - INFO - Enhanced Excel report generated: reports\reports_boernevisioncenter_com\report_boernevisioncenter_com_20250630_165153.xlsx
2025-06-30 16:52:03,391 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 16:52:03,655 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:03,914 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:04,184 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:04,438 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:04,703 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:04,969 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:05,231 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:05,486 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:05,744 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:52:06,000 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:54:15,994 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 16:54:17,785 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 16:54:17,831 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 16:54:50,244 - src.core.crawler - INFO - Discovered 45 URLs
2025-06-30 16:54:50,246 - src.utils.file_utils - INFO - Saved 45 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 16:54:54,086 - src.core.wordpress - INFO - Found WordPress API endpoint: https://boernevisioncenter.com/wp-json/wp/v2/
2025-06-30 16:54:54,087 - src.core.wordpress - INFO - Fetching data from WordPress API: https://boernevisioncenter.com/wp-json/wp/v2/
2025-06-30 16:54:54,223 - src.core.wordpress - INFO - Successfully fetched data from WordPress API.
2025-06-30 16:57:08,468 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 16:57:14,273 - src.core.google_apis - INFO - Fetched 15465 rows for 2024-06-30 to 2024-07-29
2025-06-30 16:57:16,526 - src.core.google_apis - INFO - Fetched 14271 rows for 2024-07-30 to 2024-08-29
2025-06-30 16:57:18,326 - src.core.google_apis - INFO - Fetched 14178 rows for 2024-08-30 to 2024-09-29
2025-06-30 16:57:20,296 - src.core.google_apis - INFO - Fetched 15528 rows for 2024-09-30 to 2024-10-29
2025-06-30 16:57:21,735 - src.core.google_apis - INFO - Fetched 14638 rows for 2024-10-30 to 2024-11-29
2025-06-30 16:57:23,165 - src.core.google_apis - INFO - Fetched 13484 rows for 2024-11-30 to 2024-12-29
2025-06-30 16:57:24,720 - src.core.google_apis - INFO - Fetched 13452 rows for 2024-12-30 to 2025-01-29
2025-06-30 16:57:26,132 - src.core.google_apis - INFO - Fetched 14726 rows for 2025-01-30 to 2025-02-27
2025-06-30 16:57:27,497 - src.core.google_apis - INFO - Fetched 12642 rows for 2025-02-28 to 2025-03-27
2025-06-30 16:57:28,777 - src.core.google_apis - INFO - Fetched 12375 rows for 2025-03-28 to 2025-04-27
2025-06-30 16:57:30,248 - src.core.google_apis - INFO - Fetched 13622 rows for 2025-04-28 to 2025-05-27
2025-06-30 16:57:32,076 - src.core.google_apis - INFO - Fetched 15699 rows for 2025-05-28 to 2025-06-27
2025-06-30 16:57:32,698 - src.core.google_apis - INFO - Fetched 2606 rows for 2025-06-28 to 2025-06-30
2025-06-30 16:57:32,947 - src.core.google_apis - INFO - Fetching GSC data for https://boernevisioncenter.com/ from 2024-06-30 to 2025-06-30
2025-06-30 16:57:33,422 - src.core.google_apis - INFO - Fetched 115 rows for 2024-06-30 to 2024-07-29
2025-06-30 16:57:33,932 - src.core.google_apis - INFO - Fetched 117 rows for 2024-07-30 to 2024-08-29
2025-06-30 16:57:34,424 - src.core.google_apis - INFO - Fetched 117 rows for 2024-08-30 to 2024-09-29
2025-06-30 16:57:34,871 - src.core.google_apis - INFO - Fetched 116 rows for 2024-09-30 to 2024-10-29
2025-06-30 16:57:35,348 - src.core.google_apis - INFO - Fetched 117 rows for 2024-10-30 to 2024-11-29
2025-06-30 16:57:35,855 - src.core.google_apis - INFO - Fetched 116 rows for 2024-11-30 to 2024-12-29
2025-06-30 16:57:36,355 - src.core.google_apis - INFO - Fetched 119 rows for 2024-12-30 to 2025-01-29
2025-06-30 16:57:36,814 - src.core.google_apis - INFO - Fetched 118 rows for 2025-01-30 to 2025-02-27
2025-06-30 16:57:37,198 - src.core.google_apis - INFO - Fetched 121 rows for 2025-02-28 to 2025-03-27
2025-06-30 16:57:37,589 - src.core.google_apis - INFO - Fetched 119 rows for 2025-03-28 to 2025-04-27
2025-06-30 16:57:37,978 - src.core.google_apis - INFO - Fetched 122 rows for 2025-04-28 to 2025-05-27
2025-06-30 16:57:38,409 - src.core.google_apis - INFO - Fetched 124 rows for 2025-05-28 to 2025-06-27
2025-06-30 16:57:38,915 - src.core.google_apis - INFO - Fetched 95 rows for 2025-06-28 to 2025-06-30
2025-06-30 16:57:38,919 - src.core.google_apis - INFO - Fetching GA4 data for property 260532414 from 2024-06-30 to 2025-06-30
2025-06-30 16:57:41,912 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-06-30 to 2024-07-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:42,258 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-07-30 to 2024-08-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:42,833 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-08-30 to 2024-09-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:43,151 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-09-30 to 2024-10-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:43,476 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-10-30 to 2024-11-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:44,103 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-11-30 to 2024-12-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:44,448 - src.core.google_apis - ERROR - Error fetching GA4 data for 2024-12-30 to 2025-01-29: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:44,804 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-01-30 to 2025-02-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 16:57:45,161 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-02-28 to 2025-03-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 17:59:46,895 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-03-28 to 2025-04-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 17:59:47,503 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-04-28 to 2025-05-27: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-30 17:59:49,516 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-05-28 to 2025-06-27: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 17:59:49,870 - src.core.google_apis - ERROR - Error fetching GA4 data for 2025-06-28 to 2025-06-30: <HttpError 400 when requesting https://analyticsdata.googleapis.com/v1beta/properties/260532414:runReport?alt=json returned "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema". Details: "Did you mean keyEvents? Field pageviews is not a valid metric.  For a list of valid dimensions and metrics, see https://developers.google.com/analytics/devguides/reporting/data/v1/api-schema ">
2025-06-30 17:59:49,880 - src.services.link_analysis_service - INFO - Building internal links data...
2025-06-30 17:59:49,881 - src.services.link_analysis_service - INFO - No internal links data from API, falling back to enhanced HTML parsing
2025-06-30 18:00:00,213 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 18:00:07,554 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?on_conflict=site_id%2CURL%2Csnapshot_date&columns=%22site_id%22%2C%22Page+Content%22%2C%22URL%22%2C%22Meta+Description%22%2C%22raw_html%22%2C%22H1%22%2C%22url_hash%22%2C%22snapshot_date%22%2C%22SEO+Title%22 "HTTP/2 200 OK"
2025-06-30 18:00:09,044 - src.database.supabase_client - INFO - Saved 45 pages to Supabase
2025-06-30 18:01:40,557 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?on_conflict=site_id%2CURL%2CKeyword%2CMonth&columns=%22site_id%22%2C%22Month%22%2C%22URL%22%2C%22Position%22%2C%22Clicks%22%2C%22Keyword%22%2C%22keyword_hash%22%2C%22Impressions%22%2C%22CTR%22 "HTTP/2 500 Internal Server Error"
2025-06-30 18:01:40,562 - src.database.supabase_client - ERROR - Error saving GSC keywords: {'message': 'canceling statement due to statement timeout', 'code': '57014', 'hint': None, 'details': None}
2025-06-30 18:01:43,299 - httpx - INFO - HTTP Request: POST https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?on_conflict=site_id%2CURL%2CMonth&columns=%22site_id%22%2C%22Month%22%2C%22traffic_hash%22%2C%22URL%22%2C%22Position%22%2C%22Clicks%22%2C%22Impressions%22%2C%22CTR%22 "HTTP/2 200 OK"
2025-06-30 18:01:43,735 - src.database.supabase_client - INFO - Saved 1516 GSC traffic records to Supabase
2025-06-30 18:12:58,076 - src.api.app - INFO - FastAPI application created successfully
2025-06-30 18:13:11,628 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A "HTTP/2 200 OK"
2025-06-30 18:13:11,901 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:12,176 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:12,443 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:12,705 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:12,970 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=id&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:13,227 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/pages?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:13,494 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/internal_links?select=snapshot_date&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:13,753 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_keywords?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:14,018 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/gsc_traffic?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:14,275 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/ga_data?select=Month&site_id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:22,259 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=%2A&id=eq.1 "HTTP/2 200 OK"
2025-06-30 18:13:24,128 - httpx - INFO - HTTP Request: GET https://ltrymguxcxzyofxmbutv.supabase.co/rest/v1/sites?select=id&domain=eq.boernevisioncenter.com "HTTP/2 200 OK"
2025-06-30 18:13:24,223 - src.core.crawler - INFO - Discovering URLs from homepage https://stg-boerne-boernestag.kinsta.cloud...
2025-06-30 18:14:11,061 - src.core.crawler - INFO - Discovered 45 URLs
2025-06-30 18:14:11,065 - src.utils.file_utils - INFO - Saved 45 URLs to reports\reports_boernevisioncenter_com\urls_to_crawl.txt
2025-06-30 18:14:11,066 - src.core.wordpress - INFO - Trying WordPress API endpoint: https://boernevisioncenter.com/wp-json/wp-data-exporter/v1/data
2025-06-30 18:14:13,195 - src.core.wordpress - INFO - Response status for https://boernevisioncenter.com/wp-json/wp-data-exporter/v1/data: 404
2025-06-30 18:14:13,196 - src.core.wordpress - WARNING - No WordPress API endpoint found for https://boernevisioncenter.com/
